import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { setupAuth, isAuthenticated } from "./auth";
import {
  insertLearningPlanSchema,
  insertVideoSchema,
  insertPlanVideoSchema,
  insertVideoProgressSchema,
  friendInvites,
  friendships,
  users,
  learningPlans,
  planShares,
  videos,
  planVideos,
  videoProgress,
  favoriteVideos,
  favoritePlaylists,
} from "@shared/schema";
import { z } from "zod";
import { db } from "./db";
import { eq, and, desc, sql, gte, or, inArray } from "drizzle-orm";
import { 
  sanitizeInput, 
  escapeHtml, 
  sanitizeForLog, 
  csrfProtection, 
  sanitizePath, 
  rateLimit, 
  securityHeaders 
} from "./security";

export async function registerRoutes(app: Express): Promise<Server> {
  // Apply security headers
  app.use(securityHeaders);
  
  // Apply rate limiting
  app.use(rateLimit(100, 15 * 60 * 1000)); // 100 requests per 15 minutes
  
  // Security middleware - block common attack patterns
  app.use((req, res, next) => {
    const suspiciousPatterns = [
      "/actuator",
      "/.env",
      "/wp-admin",
      "/phpmyadmin",
      "/xmlrpc.php",
    ];

    // Allow admin routes for legitimate access
    const isAdminRoute = req.path.startsWith('/admin/') || req.path.startsWith('/api/admin/');
    
    if (!isAdminRoute && suspiciousPatterns.some((pattern) => req.path.includes(pattern))) {
      console.log(
        sanitizeForLog(`🚨 Blocked suspicious request: ${req.method} ${req.path} from ${req.ip}`)
      );
      return res.status(404).end();
    }

    next();
  });

  // Auth middleware
  setupAuth(app);

  // Track user activity on authenticated requests
  app.use((req: any, res, next) => {
    if (req.user && req.user.id) {
      trackUserActivity(sanitizeInput(req.user.id), req);
    }
    next();
  });

  // Auth routes
  // Auth routes are now handled in auth.ts
  
  // Track user logout and clean up session
  app.post("/api/logout", csrfProtection, (req: any, res) => {
    if (req.user && req.user.id) {
      const userId = sanitizeInput(req.user.id);
      activeUsers.delete(userId);
      userLastActivity.delete(userId);
      userRegions.delete(userId);
    }
    
    // Destroy session completely
    req.session.destroy((err: any) => {
      if (err) {
        console.error(sanitizeForLog('Session destruction error:'), err);
      }
    });
    
    req.logout((err: any) => {
      if (err) {
        return res.status(500).json({ message: "Logout failed" });
      }
      
      // Clear session cookie
      res.clearCookie('connect.sid');
      res.json({ message: "Logged out successfully" });
    });
  });

  // Learning Plans routes
  app.get("/api/learning-plans", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const plans = await storage.getUserLearningPlans(userId);
      res.json(plans);
    } catch (error) {
      console.error("Error fetching learning plans:", error);
      res.status(500).json({ message: "Failed to fetch learning plans" });
    }
  });

  // Get learning plans with progress calculated
  app.get("/api/learning-plans-with-progress", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      
      // Get all user's learning plans
      const plans = await storage.getUserLearningPlans(userId);
      
      // Calculate progress for each plan
      const plansWithProgress = await Promise.all(
        plans.map(async (plan) => {
          try {
            // Get plan videos
            const planVideos = await storage.getPlanVideos(plan.id);
            const totalVideos = planVideos.length;
            
            // Get user progress for this plan (now includes all videos)
            const userProgress = await storage.getUserProgressForPlan(userId, plan.id);
            const completedVideos = userProgress.filter(p => p.isCompleted === true).length;
            
            // Use userProgress.length as the definitive count (includes all plan videos)
            const actualVideoCount = userProgress.length;
            const progressPercentage = actualVideoCount > 0 ? Math.round((completedVideos / actualVideoCount) * 100) : 0;
            
            console.log(`📊 Plan "${plan.title}" (ID: ${plan.id}): ${completedVideos}/${actualVideoCount} = ${progressPercentage}%`);
            
            // Validate counts match
            if (totalVideos !== actualVideoCount) {
              console.warn(`⚠️ Video count mismatch for plan ${plan.id}: planVideos=${totalVideos}, userProgress=${actualVideoCount}`);
            }
            
            return {
              ...plan,
              videoCount: actualVideoCount,
              completedVideos,
              progress: progressPercentage
            };
          } catch (error) {
            console.error(`Error calculating progress for plan ${plan.id}:`, error);
            return {
              ...plan,
              videoCount: 0,
              completedVideos: 0,
              progress: 0
            };
          }
        })
      );
      
      res.json(plansWithProgress);
    } catch (error) {
      console.error("Error fetching learning plans with progress:", error);
      res.status(500).json({ message: "Failed to fetch learning plans with progress" });
    }
  });

  app.post("/api/learning-plans", isAuthenticated, csrfProtection, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const { title, description, isPublic } = req.body;
      
      // Sanitize inputs
      const sanitizedTitle = sanitizeInput(title);
      const sanitizedDescription = sanitizeInput(description || '');
      
      console.log(sanitizeForLog('📝 Creating plan:'), { userId, title: sanitizedTitle, description: sanitizedDescription, isPublic });

      // Generate a simple slug
      const slug = sanitizedTitle.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '') + '-' + Date.now();

      const planData = {
        userId,
        title: sanitizedTitle,
        description: sanitizedDescription,
        slug,
        isPublic: isPublic || false,
        isActive: true
      };
      
      console.log(sanitizeForLog('📝 Plan data to create:'), planData);

      const plan = await storage.createLearningPlan(planData);
      console.log(sanitizeForLog('✅ Plan created successfully:'), plan);
      res.status(201).json(plan);
    } catch (error) {
      console.error(sanitizeForLog("❌ Error creating learning plan:"), error);
      res.status(500).json({ 
        message: "Failed to create learning plan",
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // Get learning plan by ID (legacy support)
  app.get("/api/learning-plans/:id", isAuthenticated, async (req: any, res) => {
    try {
      const planId = parseInt(req.params.id);
      if (isNaN(planId) || planId <= 0) {
        return res.status(400).json({ message: "Invalid plan ID" });
      }
      const userId = req.user.id;

      // Check user access permissions
      const permissions = await storage.canUserAccessPlan(planId, userId);
      if (!permissions.canView) {
        return res
          .status(403)
          .json({ message: "Access denied to this learning plan" });
      }

      const plan = await storage.getLearningPlan(planId);
      if (!plan) {
        return res.status(404).json({ message: "Learning plan not found" });
      }

      res.json({ ...plan, permissions });
    } catch (error) {
      console.error("Error fetching learning plan:", error);
      res.status(500).json({ message: "Failed to fetch learning plan" });
    }
  });

  // Get learning plan by slug (professional URLs)
  app.get(
    "/api/learning-plans/slug/:slug",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const { slug } = req.params;
        const userId = req.user.id;

        const plan = await storage.getLearningPlanBySlug(slug);
        if (!plan) {
          return res.status(404).json({ message: "Learning plan not found" });
        }

        // Check user access permissions
        const permissions = await storage.canUserAccessPlan(plan.id, userId);
        
        if (!permissions.canView) {
          return res
            .status(403)
            .json({ message: "Access denied to this learning plan" });
        }

        res.json({ ...plan, permissions });
      } catch (error) {
        console.error("Error fetching learning plan by slug:", error);
        res.status(500).json({ message: "Failed to fetch learning plan" });
      }
    }
  );

  app.get(
    "/api/learning-plans/:id/videos",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const planId = parseInt(req.params.id);
        if (isNaN(planId) || planId <= 0) {
          return res.status(400).json({ message: "Invalid plan ID" });
        }
        const videos = await storage.getPlanVideos(planId);
        res.json(videos);
      } catch (error) {
        console.error("Error fetching plan videos:", error);
        res.status(500).json({ message: "Failed to fetch plan videos" });
      }
    }
  );

  // YouTube API integration with rate limiting
  const searchRateLimit = new Map();
  
  // Content filtering function - comprehensive blocking of harmful adult content
  const isEducationalContent = (title: string, description: string, channelTitle: string) => {
    const content = `${title} ${description} ${channelTitle}`.toLowerCase();
    
    // Comprehensive list of blocked keywords for adult/inappropriate content
    const blockedKeywords = [
      // Explicit adult content
      'xxx', 'porn', 'pornography', 'nude', 'naked', 'explicit', 'nsfw', 'erotic', 'sexual',
      'sex', 'sexy', 'adult', 'mature', 'hardcore', 'softcore', 'fetish', 'bdsm',
      'onlyfans', 'escort', 'prostitute', 'strip', 'stripper', 'webcam', 'cam girl',
      
      // Gambling and inappropriate activities
      'gambling', 'casino', 'poker', 'betting', 'slots', 'jackpot',
      
      // Violence and inappropriate content
      'violence', 'violent', 'kill', 'murder', 'death', 'suicide', 'self-harm',
      'drug', 'drugs', 'cocaine', 'heroin', 'marijuana', 'weed', 'alcohol', 'drunk',
      
      // Hate speech and inappropriate language
      'hate', 'racist', 'terrorism', 'terrorist', 'extremist'
    ];
    
    // Educational exceptions - words that might appear in legitimate educational content
    const educationalExceptions = [
      'class', 'classical', 'assessment', 'mass', 'passage', 'glass', 'grass',
      'assignment', 'assistance', 'associate', 'association', 'assumption',
      'harassment', 'embarrass', 'address', 'process', 'success', 'access',
      'necessary', 'possess', 'express', 'suppress', 'impress', 'compress',
      'business', 'witness', 'fitness', 'illness', 'wellness', 'darkness',
      'analysis', 'synthesis', 'thesis', 'basis', 'crisis', 'emphasis',
      'sexual education', 'sex education', 'reproductive health', 'biology',
      'anatomy', 'physiology', 'psychology', 'sociology', 'anthropology'
    ];
    
    // Check for educational exceptions first
    const hasEducationalException = educationalExceptions.some(exception => {
      return content.includes(exception);
    });
    
    // If it's educational content, allow it
    if (hasEducationalException) {
      // But still block if it contains explicit adult keywords
      const explicitKeywords = ['xxx', 'porn', 'pornography', 'nude', 'naked', 'explicit', 'nsfw', 'erotic', 'onlyfans', 'escort'];
      const hasExplicitContent = explicitKeywords.some(keyword => {
        const regex = new RegExp(`\\b${keyword}\\b`, 'i');
        return regex.test(content);
      });
      return !hasExplicitContent;
    }
    
    // Use word boundaries to avoid false positives (e.g., "class" containing "ass")
    const hasBlockedContent = blockedKeywords.some(keyword => {
      const regex = new RegExp(`\\b${keyword}\\b`, 'i');
      const match = regex.test(content);
      if (match) {
        console.log(`🚫 Content blocked for keyword: "${keyword}" in: ${title}`);
      }
      return match;
    });
    
    return !hasBlockedContent;
  };

  app.get("/api/youtube/search", isAuthenticated, async (req: any, res) => {
    // Rate limiting: max 20 searches per minute per user
    const userId = req.user.id;
    const now = Date.now();
    const userRequests = searchRateLimit.get(userId) || [];
    const recentRequests = userRequests.filter(time => now - time < 60000);
    
    if (recentRequests.length >= 20) {
      return res.status(429).json({ message: "Too many search requests. Please wait a moment." });
    }
    
    searchRateLimit.set(userId, [...recentRequests, now]);
    try {
      const { q, pageToken, maxResults = 6 } = req.query;

      if (!q) {
        return res.status(400).json({ message: "Search query is required" });
      }

      const YOUTUBE_API_KEY =
        process.env.YOUTUBE_API_KEY || process.env.YOUTUBE_DATA_API_KEY;

      if (!YOUTUBE_API_KEY) {
        return res
          .status(500)
          .json({ message: "YouTube API key not configured" });
      }

      // Build URL with content filtering
      let apiUrl = `https://www.googleapis.com/youtube/v3/search?part=snippet&type=video&q=${encodeURIComponent(
        q
      )}&maxResults=${maxResults}&order=relevance&safeSearch=strict&videoEmbeddable=true&key=${YOUTUBE_API_KEY}`;

      if (pageToken) {
        apiUrl += `&pageToken=${pageToken}`;
      }

      const startTime = Date.now();
      const response = await fetch(apiUrl);
      const responseTime = Date.now() - startTime;
      
      // Track API usage
      trackAPICall('youtube', response.status, responseTime);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('❌ YouTube API error:', response.status, errorData);
        console.error('🔗 API URL:', apiUrl);
        console.error('🔑 API Key (first 10 chars):', YOUTUBE_API_KEY?.substring(0, 10) + '...');
        
        // Check for specific API key issues
        if (response.status === 403) {
          if (errorData.error?.reason === 'API_KEY_HTTP_REFERRER_BLOCKED') {
            console.error('❌ YouTube API Key has HTTP referrer restrictions enabled');
            return res.status(500).json({ 
              message: "YouTube API configuration error. Please check API key settings.",
              details: "HTTP referrer restrictions are blocking the request"
            });
          }
          if (errorData.error?.reason === 'API_KEY_INVALID') {
            console.error('❌ YouTube API Key is invalid');
            return res.status(500).json({ 
              message: "Invalid YouTube API key",
              details: "Please check your API key configuration"
            });
          }
        }
        
        return res.status(response.status).json({
          message: `YouTube API error: ${response.status}`,
          details: errorData.error?.message || response.statusText,
          error: errorData
        });
      }

      const data = await response.json();

      // Filter videos for educational content
      const filteredVideos = data.items?.filter((item: any) => {
        return isEducationalContent(
          item.snippet.title,
          item.snippet.description || '',
          item.snippet.channelTitle
        );
      }) || [];

      const videos = filteredVideos.map((item: any) => ({
        youtubeId: item.id.videoId,
        title: item.snippet.title,
        description: item.snippet.description,
        thumbnailUrl:
          item.snippet.thumbnails.medium?.url ||
          item.snippet.thumbnails.default?.url,
        channelTitle: item.snippet.channelTitle,
        publishedAt: item.snippet.publishedAt,
      }));

      // Return videos with pagination info
      res.json({ 
        videos,
        nextPageToken: data.nextPageToken,
        prevPageToken: data.prevPageToken,
        totalResults: data.pageInfo?.totalResults,
        resultsPerPage: data.pageInfo?.resultsPerPage
      });
    } catch (error) {
      console.error("Error searching YouTube:", error);
      res.status(500).json({ message: "Failed to search YouTube videos" });
    }
  });

  // Video routes
  app.post("/api/videos", isAuthenticated, csrfProtection, async (req: any, res) => {
    try {
      const videoData = insertVideoSchema.parse(req.body);
      
      // Sanitize video data
      videoData.title = sanitizeInput(videoData.title);
      videoData.description = sanitizeInput(videoData.description || '');
      videoData.channelTitle = sanitizeInput(videoData.channelTitle);
      
      // Content filtering check
      if (!isEducationalContent(videoData.title, videoData.description || '', videoData.channelTitle)) {
        console.log(sanitizeForLog(`🚫 Content filtered during creation: ${videoData.title} by ${videoData.channelTitle}`));
        return res.status(400).json({ 
          message: "Content not suitable for educational platform",
          code: "INAPPROPRIATE_CONTENT"
        });
      }

      // Check if video already exists
      let video = await storage.getVideoByYoutubeId(videoData.youtubeId);

      if (!video) {
          // Get additional video details from YouTube API
        const YOUTUBE_API_KEY =
          process.env.YOUTUBE_API_KEY || process.env.YOUTUBE_DATA_API_KEY;

        if (YOUTUBE_API_KEY) {
          const response = await fetch(
            `https://www.googleapis.com/youtube/v3/videos?part=snippet,contentDetails,statistics&id=${videoData.youtubeId}&key=${YOUTUBE_API_KEY}`
          );

          if (response.ok) {
            const data = await response.json();
            const item = data.items?.[0];

            if (item) {
              // Additional content check from full video details
              if (!isEducationalContent(
                sanitizeInput(item.snippet.title),
                sanitizeInput(item.snippet.description || ''),
                sanitizeInput(item.snippet.channelTitle)
              )) {
                console.log(sanitizeForLog(`🚫 Content filtered: ${item.snippet.title} by ${item.snippet.channelTitle}`));
                return res.status(400).json({ 
                  message: "Content not suitable for educational platform",
                  code: "INAPPROPRIATE_CONTENT"
                });
              }
              
              videoData.duration = item.contentDetails.duration;
              videoData.viewCount = parseInt(item.statistics.viewCount);
            }
          }
        }

        video = await storage.createVideo(videoData);
      }

      res.json(video);
    } catch (error) {
      console.error("Error creating video:", error);
      res.status(500).json({ message: "Failed to create video" });
    }
  });

  // Delete video from library
  app.delete("/api/videos/:id", isAuthenticated, csrfProtection, async (req: any, res) => {
    try {
      const videoId = parseInt(req.params.id);
      await storage.deleteVideo(videoId);
      res.json({ message: "Video deleted successfully" });
    } catch (error) {
      console.error("Error deleting video:", error);
      res.status(500).json({ message: "Failed to delete video" });
    }
  });

  app.post(
    "/api/learning-plans/:id/videos",
    isAuthenticated,
    csrfProtection,
    async (req: any, res) => {
      try {
        const planId = parseInt(req.params.id);
        const { videoId } = req.body;

        // Check if video already exists in this plan
        const existingVideos = await storage.getPlanVideos(planId);
        const isDuplicate = existingVideos.some(pv => pv.videoId === videoId);
        
        if (isDuplicate) {
          return res.status(200).json({ 
            message: "Video is already in this plan",
            code: "ALREADY_EXISTS",
            success: true
          });
        }

        const orderIndex = existingVideos.length;
        const planVideoData = insertPlanVideoSchema.parse({
          planId,
          videoId,
          orderIndex,
        });

        const planVideo = await storage.addVideoToPlan(planVideoData);
        res.status(201).json(planVideo);
      } catch (error) {
        console.error("Error adding video to plan:", error);
        res.status(500).json({ message: "Failed to add video to plan" });
      }
    }
  );

  // Get videos for a specific plan
  app.get(
    "/api/learning-plans/:id/videos",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const planId = parseInt(req.params.id);
        const planVideos = await storage.getPlanVideos(planId);
        res.json(planVideos);
      } catch (error) {
        console.error("Error fetching plan videos:", error);
        res.status(500).json({ message: "Failed to fetch plan videos" });
      }
    }
  );

  // Progress tracking routes
  app.put("/api/video-progress", isAuthenticated, csrfProtection, async (req: any, res) => {
    try {
      const userId = req.user.id;

      // Check if video exists first
      const video = await storage.getVideo(req.body.videoId);
      if (!video) {
        return res.status(404).json({ message: "Video not found" });
      }

      const progressData = insertVideoProgressSchema.parse({
        ...req.body,
        userId,
      });

      const progress = await storage.updateVideoProgress(progressData);

      res.json(progress);
    } catch (error) {
      console.error("=== VIDEO PROGRESS ERROR ===");
      console.error("Error updating video progress:", error);
      if (error instanceof Error) {
        console.error("Error message:", error.message);
        console.error("Error stack:", error.stack);
      }
      res.status(500).json({
        message: "Failed to update video progress",
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  });

  app.get(
    "/api/progress/plan/:planId",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const userId = req.user.id;
        const planId = parseInt(req.params.planId);
        
        console.log(sanitizeForLog(`🔍 API: Getting progress for plan ${planId}, user ${userId}`));

        const progress = await storage.getUserProgressForPlan(userId, planId);
        
        console.log(sanitizeForLog(`📊 API: Returning ${progress.length} progress records`));
        const completedCount = progress.filter(p => p.isCompleted === true).length;
        console.log(sanitizeForLog(`📊 API: Completed videos: ${completedCount}`));
        
        // Direct database check for debugging
        const directCheck = await db
          .select({
            videoId: videoProgress.videoId,
            isCompleted: videoProgress.isCompleted,
            currentTime: videoProgress.currentTime
          })
          .from(videoProgress)
          .innerJoin(planVideos, eq(videoProgress.videoId, planVideos.videoId))
          .where(
            and(
              eq(videoProgress.userId, userId),
              eq(planVideos.planId, planId),
              eq(videoProgress.isCompleted, true)
            )
          );
        
        console.log(sanitizeForLog(`🔍 Direct DB check - Completed videos in plan ${planId}:`), directCheck.length);
        directCheck.forEach(v => console.log(sanitizeForLog(`  ✅ Video ${v.videoId}: completed=${v.isCompleted}, time=${v.currentTime}s`)));
        
        res.json(progress);
      } catch (error) {
        console.error("Error fetching plan progress:", error);
        res.status(500).json({ message: "Failed to fetch plan progress" });
      }
    }
  );

  // Dashboard analytics routes
  app.get("/api/analytics/daily", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const stats = await storage.getUserDailyStats(userId);
      res.json(stats);
    } catch (error) {
      console.error("Error fetching daily stats:", error);
      res.status(500).json({ message: "Failed to fetch daily stats" });
    }
  });

  app.get("/api/analytics/weekly", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const stats = await storage.getUserWeeklyProgress(userId);
      res.json(stats);
    } catch (error) {
      console.error("Error fetching weekly progress:", error);
      res.status(500).json({ message: "Failed to fetch weekly progress" });
    }
  });

  app.get("/api/analytics/monthly", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const stats = await storage.getUserMonthlyStats(userId);
      res.json(stats);
    } catch (error) {
      console.error("Error fetching monthly stats:", error);
      res.status(500).json({ message: "Failed to fetch monthly stats" });
    }
  });

  // Achievements routes
  app.get("/api/achievements", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const achievements = await storage.getUserAchievements(userId);
      res.json(achievements);
    } catch (error) {
      console.error("Error fetching achievements:", error);
      res.status(500).json({ message: "Failed to fetch achievements" });
    }
  });

  // Continue learning endpoint
  app.get("/api/continue-learning", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;

      // Get user's video progress for videos that are not completed
      const progressData = await storage.getUserIncompleteVideos(userId);
      
      res.json(progressData);
    } catch (error) {
      console.error("Error fetching continue learning data:", error);
      res
        .status(500)
        .json({ message: "Failed to fetch continue learning data" });
    }
  });

  // Personalized recommendations endpoint
  app.get("/api/recommendations", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const limit = parseInt(req.query.limit as string) || 10;
      
      console.log(sanitizeForLog(`🎯 Fetching recommendations for user ${userId}, limit: ${limit}`));

      const recommendations = await storage.getPersonalizedRecommendations(
        userId,
        limit
      );
      
      console.log(sanitizeForLog(`✅ Found ${recommendations.length} recommendations`));
      res.json(recommendations);
    } catch (error) {
      console.error("❌ Error fetching recommendations:", error);
      res.status(500).json({ message: "Failed to fetch recommendations", error: error.message });
    }
  });

  // User watching patterns endpoint
  app.get("/api/user/patterns", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;

      const patterns = await storage.getUserWatchingPatterns(userId);
      res.json(patterns);
    } catch (error) {
      console.error("Error fetching user patterns:", error);
      res.status(500).json({ message: "Failed to fetch user patterns" });
    }
  });

  // Get video by ID
  app.get("/api/videos/:id", isAuthenticated, async (req: any, res) => {
    try {
      const videoId = parseInt(req.params.id);
      const video = await storage.getVideo(videoId);
      if (!video) {
        return res.status(404).json({ message: "Video not found" });
      }
      res.json(video);
    } catch (error) {
      console.error("Error fetching video:", error);
      res.status(500).json({ message: "Failed to fetch video" });
    }
  });

  // Get video by YouTube ID
  app.get("/api/videos/youtube/:youtubeId", isAuthenticated, async (req: any, res) => {
    try {
      const { youtubeId } = req.params;
      const video = await storage.getVideoByYoutubeId(youtubeId);
      if (!video) {
        return res.status(404).json({ message: "Video not found" });
      }
      res.json(video);
    } catch (error) {
      console.error("Error fetching video by YouTube ID:", error);
      res.status(500).json({ message: "Failed to fetch video" });
    }
  });

  // Get video progress
  app.get(
    "/api/video-progress/:videoId",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const userId = req.user.id;
        const videoId = parseInt(req.params.videoId);
        const progress = await storage.getUserVideoProgress(userId, videoId);
        res.json(progress);
      } catch (error) {
        console.error("Error fetching video progress:", error);
        res.status(500).json({ message: "Failed to fetch video progress" });
      }
    }
  );

  // Delete video from plan
  app.delete(
    "/api/learning-plans/:planId/videos/:videoId",
    isAuthenticated,
    csrfProtection,
    async (req: any, res) => {
      try {
        const planId = parseInt(req.params.planId);
        const videoId = parseInt(req.params.videoId);

        await storage.removeVideoFromPlan(planId, videoId);
        res.status(204).send();
      } catch (error) {
        console.error("Error removing video from plan:", error);
        res.status(500).json({ message: "Failed to remove video from plan" });
      }
    }
  );

  // Update learning plan
  app.put("/api/learning-plans/:id", isAuthenticated, csrfProtection, async (req: any, res) => {
    try {
      const planId = parseInt(req.params.id);
      const updates = req.body;

      const updatedPlan = await storage.updateLearningPlan(planId, updates);
      res.json(updatedPlan);
    } catch (error) {
      console.error("Error updating learning plan:", error);
      res.status(500).json({ message: "Failed to update learning plan" });
    }
  });

  // Delete learning plan
  app.delete(
    "/api/learning-plans/:id",
    isAuthenticated,
    csrfProtection,
    async (req: any, res) => {
      try {
        const planId = parseInt(req.params.id);
        await storage.deleteLearningPlan(planId);
        res.status(204).send();
      } catch (error) {
        console.error("Error deleting learning plan:", error);
        res.status(500).json({ message: "Failed to delete learning plan" });
      }
    }
  );

  // Plan sharing routes
  app.post(
    "/api/learning-plans/:id/share",
    isAuthenticated,
    csrfProtection,
    async (req: any, res) => {
      try {
        const planId = parseInt(req.params.id);
        const { email, friendId } = req.body;
        const userId = req.user.id;
        
        if (email) {
          // Share via email
          const [existingUser] = await db.select().from(users).where(eq(users.email, email)).limit(1);
          const fromUser = await storage.getUser(userId);
          const originalPlan = await storage.getLearningPlan(planId);
          
          if (existingUser) {
            // User exists - copy plan directly
            const shareToken = await storage.generateShareTokenForPlan(planId);
            const newPlan = await storage.copyPlanToUser(shareToken, existingUser.id, '(Shared)');
            
            // Create plan share record in database for notification
            
            try {
              await db.insert(planShares).values({
                planId: newPlan.id,
                sharedByUserId: userId,
                sharedWithUserId: existingUser.id,
                canAddVideos: false,
                canReshare: false
              });
            } catch (shareError) {
              console.error('❌ Failed to create plan share record:', shareError);
            }
            
            res.json({
              success: true,
              message: `Plan shared with ${email} successfully!`,
              userExists: true,
              planId: newPlan.id
            });
          } else {
            // User doesn't exist - generate share link and send email
            const shareToken = await storage.generateShareTokenForPlan(planId);
            const shareUrl = `${req.protocol}://${req.hostname}/shared/${shareToken}?email=${encodeURIComponent(email)}`;
            
            // Send email
            let emailSent = false;
            try {
              const { sendPlanShareEmail } = await import("./email");
              const fromUserName = fromUser?.firstName || fromUser?.email || "A friend";
              emailSent = await sendPlanShareEmail(
                email,
                fromUserName,
                originalPlan?.title || 'Learning Plan',
                shareUrl
              );
            } catch (emailError) {
              console.error("Email sending failed:", emailError);
            }
            
            res.json({
              success: true,
              message: emailSent 
                ? `Share link sent to ${email} successfully!`
                : `Share link generated for ${email}. Email delivery failed.`,
              userExists: false,
              shareToken,
              shareUrl,
              emailSent
            });
          }
        } else if (friendId) {
          // Share with friend directly
          const shareToken = await storage.generateShareTokenForPlan(planId);
          const newPlan = await storage.copyPlanToUser(shareToken, friendId, '(Shared)');
          
          const friend = await storage.getUser(friendId);
          res.json({
            success: true,
            message: `Plan shared with ${friend?.firstName || friend?.email} successfully!`,
            userExists: true,
            planId: newPlan.id
          });
        } else {
          // Generate general share link
          const shareToken = await storage.generateShareTokenForPlan(planId);
          res.json({
            shareToken,
            shareUrl: `${req.protocol}://${req.hostname}/shared/${shareToken}`,
          });
        }
      } catch (error) {
        console.error("Error sharing plan:", error);
        console.error("Error details:", error instanceof Error ? error.message : error);
        console.error("Stack trace:", error instanceof Error ? error.stack : 'No stack trace');
        res.status(500).json({ 
          message: "Failed to share plan",
          error: error instanceof Error ? error.message : "Unknown error"
        });
      }
    }
  );

  app.get("/api/shared-plan/:shareToken", async (req, res) => {
    try {
      const { shareToken } = req.params;
      const { email } = req.query;
      
      const plan = await storage.getLearningPlanByShareToken(shareToken);
      if (!plan) {
        return res.status(404).json({ message: "Plan not found" });
      }

      const planVideos = await storage.getPlanVideos(plan.id);
      
      // Check if user exists for the email
      let userExists = false;
      if (email) {
        const existingUser = await storage.getUserByEmail(email as string);
        userExists = !!existingUser;
      }
      
      res.json({ 
        plan, 
        videos: planVideos,
        suggestedEmail: email,
        userExists
      });
    } catch (error) {
      console.error("Error fetching shared plan:", error);
      res.status(500).json({ message: "Failed to fetch shared plan" });
    }
  });

  app.post(
    "/api/shared-plan/:shareToken/accept",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const { shareToken } = req.params;
        const userId = req.user.id;

        const newPlan = await storage.copyPlanToUser(shareToken, userId, '(Shared)');
        res.json(newPlan);
      } catch (error) {
        console.error("Error accepting shared plan:", error);
        res.status(500).json({ message: "Failed to accept shared plan" });
      }
    }
  );

  // Videos endpoint for explore page
  app.get("/api/videos", async (req, res) => {
    try {
      const allVideos = await storage.getAllVideos();
      res.json(allVideos);
    } catch (error) {
      console.error("Error fetching videos:", error);
      res.status(500).json({ message: "Failed to fetch videos" });
    }
  });

  // YouTube video by ID endpoint
  app.get("/api/youtube/video/:videoId", isAuthenticated, async (req: any, res) => {
    try {
      const { videoId } = req.params;
      const YOUTUBE_API_KEY = process.env.YOUTUBE_API_KEY || process.env.YOUTUBE_DATA_API_KEY;

      if (!YOUTUBE_API_KEY) {
        return res.status(500).json({ message: "YouTube API key not configured" });
      }

      const apiUrl = `https://www.googleapis.com/youtube/v3/videos?part=snippet,contentDetails,statistics&id=${videoId}&key=${YOUTUBE_API_KEY}`;
      
      const startTime = Date.now();
      const response = await fetch(apiUrl);
      const responseTime = Date.now() - startTime;
      
      trackAPICall('youtube', response.status, responseTime);

      if (!response.ok) {
        return res.status(response.status).json({ message: "Failed to fetch video details" });
      }

      const data = await response.json();
      const item = data.items?.[0];
      
      if (!item) {
        return res.status(404).json({ message: "Video not found" });
      }

      const video = {
        youtubeId: item.id,
        title: item.snippet.title,
        description: item.snippet.description,
        thumbnailUrl: item.snippet.thumbnails.medium?.url || item.snippet.thumbnails.default?.url,
        channelTitle: item.snippet.channelTitle,
        publishedAt: item.snippet.publishedAt,
        duration: item.contentDetails.duration,
        viewCount: parseInt(item.statistics.viewCount || '0'),
      };

      res.json(video);
    } catch (error) {
      console.error("Error fetching YouTube video:", error);
      res.status(500).json({ message: "Failed to fetch video details" });
    }
  });

  // Helper function to parse YouTube duration (PT4M13S) to seconds
  const parseDuration = (duration) => {
    if (!duration) return 0;
    const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
    if (!match) return 0;
    const hours = parseInt(match[1]) || 0;
    const minutes = parseInt(match[2]) || 0;
    const seconds = parseInt(match[3]) || 0;
    return hours * 3600 + minutes * 60 + seconds;
  };

  // YouTube playlist import endpoint
  app.post("/api/youtube/import-playlist", isAuthenticated, async (req: any, res) => {
    try {
      const { playlistUrl } = req.body;
      const userId = req.user.id;
      
      if (!playlistUrl) {
        return res.status(400).json({ message: "Playlist URL is required" });
      }
      
      // Extract playlist ID from URL
      const playlistMatch = playlistUrl.match(/(?:list=)([a-zA-Z0-9_-]+)/);
      if (!playlistMatch) {
        return res.status(400).json({ message: "Invalid YouTube playlist URL" });
      }
      
      const playlistId = playlistMatch[1];
      const YOUTUBE_API_KEY = process.env.YOUTUBE_API_KEY;
      
      if (!YOUTUBE_API_KEY) {
        return res.status(500).json({ message: "YouTube API not configured" });
      }
      
      // Get playlist details
      const playlistResponse = await fetch(
        `https://www.googleapis.com/youtube/v3/playlists?part=snippet&id=${playlistId}&key=${YOUTUBE_API_KEY}`
      );
      
      if (!playlistResponse.ok) {
        const errorText = await playlistResponse.text();
        console.error('YouTube playlist API error:', playlistResponse.status, errorText);
        return res.status(400).json({ message: "Failed to fetch playlist details", details: errorText });
      }
      
      const playlistData = await playlistResponse.json();
      const playlist = playlistData.items?.[0];
      
      if (!playlist) {
        return res.status(404).json({ message: "Playlist not found" });
      }
      
      // Get playlist videos
      const videosResponse = await fetch(
        `https://www.googleapis.com/youtube/v3/playlistItems?part=snippet&playlistId=${playlistId}&maxResults=50&key=${YOUTUBE_API_KEY}`
      );
      
      if (!videosResponse.ok) {
        const errorText = await videosResponse.text();
        console.error('YouTube playlist videos API error:', videosResponse.status, errorText);
        return res.status(400).json({ message: "Failed to fetch playlist videos", details: errorText });
      }
      
      const videosData = await videosResponse.json();
      const videos = videosData.items || [];
      
      if (videos.length === 0) {
        return res.status(400).json({ message: "No videos found in playlist" });
      }
      
      // Get video details including duration for all videos (optional)
      let videoDetails = [];
      let totalDurationSeconds = 0;
      
      try {
        const videoIds = videos.map(item => item.snippet.resourceId.videoId).join(',');
        const videoDetailsResponse = await fetch(
          `https://www.googleapis.com/youtube/v3/videos?part=contentDetails,snippet,statistics&id=${videoIds}&key=${YOUTUBE_API_KEY}`
        );
        
        if (videoDetailsResponse.ok) {
          const videoDetailsData = await videoDetailsResponse.json();
          videoDetails = videoDetailsData.items || [];
          
          // Calculate total duration
          totalDurationSeconds = videoDetails.reduce((total, video) => {
            const duration = video.contentDetails.duration;
            const seconds = parseDuration(duration);
            return total + seconds;
          }, 0);
        }
      } catch (durationError) {
        console.warn('Could not fetch video durations, continuing without duration info:', durationError.message);
      }
      
      // Create learning plan
      const slug = playlist.snippet.title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '') + '-' + Date.now();
      const plan = await storage.createLearningPlan({
        userId,
        title: playlist.snippet.title,
        description: playlist.snippet.description || `Imported from YouTube playlist`,
        slug,
        isActive: true,
      });
      
      // Add videos to plan
      let addedCount = 0;
      for (let i = 0; i < videos.length; i++) {
        const item = videos[i];
        const videoId = item.snippet.resourceId.videoId;
        
        try {
          // Check if video exists, create if not
          let video = await storage.getVideoByYoutubeId(videoId);
          if (!video) {
            // Find corresponding video details
            const details = videoDetails.find(v => v.id === videoId);
            
            video = await storage.createVideo({
              youtubeId: videoId,
              title: item.snippet.title,
              description: item.snippet.description || '',
              thumbnailUrl: item.snippet.thumbnails?.medium?.url || item.snippet.thumbnails?.default?.url || '',
              channelTitle: item.snippet.channelTitle,
              publishedAt: new Date(item.snippet.publishedAt),
              duration: details?.contentDetails?.duration || null,
              viewCount: details?.statistics?.viewCount ? parseInt(details.statistics.viewCount) : null,
            });
          }
          
          // Add to plan
          await storage.addVideoToPlan({
            planId: plan.id,
            videoId: video.id,
            orderIndex: i,
          });
          
          addedCount++;
        } catch (error) {
          console.error(`Failed to add video ${videoId}:`, error);
        }
      }
      
      // Format total duration for display
      const formatDuration = (seconds) => {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        if (hours > 0) {
          return `${hours}h ${minutes}m`;
        }
        return `${minutes}m`;
      };
      
      res.json({
        success: true,
        title: playlist.snippet.title,
        videoCount: addedCount,
        planId: plan.id,
        slug: plan.slug,
        totalDuration: formatDuration(totalDurationSeconds),
        totalDurationSeconds: totalDurationSeconds,
      });
    } catch (error) {
      console.error("Playlist import error:", error);
      res.status(500).json({ message: "Failed to import playlist" });
    }
  });

  // YouTube search endpoint
  app.get("/api/youtube/search", async (req, res) => {
    try {
      const { q, pageToken, maxResults = 6 } = req.query;
      const YOUTUBE_API_KEY =
        process.env.YOUTUBE_API_KEY || process.env.YOUTUBE_DATA_API_KEY;

      if (!YOUTUBE_API_KEY) {
        return res
          .status(500)
          .json({ message: "YouTube API key not configured" });
      }

      if (!q) {
        return res.status(400).json({ message: "Search query is required" });
      }

      // Build URL with content filtering
      let apiUrl = `https://www.googleapis.com/youtube/v3/search?part=snippet&maxResults=${maxResults}&q=${encodeURIComponent(
        q as string
      )}&type=video&order=relevance&safeSearch=strict&videoEmbeddable=true&key=${YOUTUBE_API_KEY}`;

      // Add pageToken if provided
      if (pageToken) {
        apiUrl += `&pageToken=${pageToken}`;
      }

      const startTime = Date.now();
      const response = await fetch(apiUrl);
      const responseTime = Date.now() - startTime;
      
      // Track API usage
      trackAPICall('youtube', response.status, responseTime);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error("YouTube API error:", response.status, errorData);
        throw new Error(`YouTube API request failed: ${response.status}`);
      }

      const data = await response.json();
      
      // Filter videos for educational content
      const filteredVideos = data.items?.filter((item: any) => {
        return isEducationalContent(
          item.snippet.title,
          item.snippet.description || '',
          item.snippet.channelTitle
        );
      }) || [];
      
      const videos = filteredVideos.map((item: any) => ({
        youtubeId: item.id.videoId,
        title: item.snippet.title,
        description: item.snippet.description,
        thumbnailUrl:
          item.snippet.thumbnails.medium?.url ||
          item.snippet.thumbnails.default?.url,
        channelTitle: item.snippet.channelTitle,
        publishedAt: item.snippet.publishedAt,
      }));

      // Return videos with pagination info
      res.json({ 
        videos,
        nextPageToken: data.nextPageToken,
        prevPageToken: data.prevPageToken,
        totalResults: data.pageInfo?.totalResults,
        resultsPerPage: data.pageInfo?.resultsPerPage
      });
    } catch (error) {
      console.error("Error searching YouTube:", error);
      res.status(500).json({ message: "Failed to search YouTube" });
    }
  });



  // AI Mood-based recommendations
  app.get(
    "/api/recommendations/mood/:mood/:timeContext",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const userId = req.user.id;
        const { mood, timeContext } = req.params;
        console.log(`🎯 Mood API called: user=${userId}, mood=${mood}, time=${timeContext}`);
        
        const recommendations = await storage.getMoodBasedRecommendations(
          userId,
          mood,
          timeContext
        );
        
        console.log(`✅ Mood recommendations found: ${recommendations.length} videos`);
        res.json(recommendations);
      } catch (error) {
        console.error("❌ Error fetching mood recommendations:", error);
        res
          .status(500)
          .json({ message: "Failed to fetch mood recommendations", error: error.message });
      }
    }
  );

  // Related videos based on current video
  app.get("/api/videos/:videoId/related", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const videoId = parseInt(req.params.videoId);
      const limit = parseInt(req.query.limit as string) || 6;
      
      console.log(`🔗 Related videos API called: user=${userId}, video=${videoId}, limit=${limit}`);
      
      const relatedVideos = await storage.getRelatedVideos(userId, videoId, limit);
      
      console.log(`✅ Returning ${relatedVideos.length} related videos`);
      res.json(relatedVideos);
    } catch (error) {
      console.error("❌ Error fetching related videos:", error);
      res.status(500).json({ message: "Failed to fetch related videos", error: error.message });
    }
  });

  // User mood profile
  app.get("/api/user/mood-profile", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const profile = await storage.getUserMoodProfile(userId);
      res.json(profile);
    } catch (error) {
      console.error("Error fetching user mood profile:", error);
      res.status(500).json({ message: "Failed to fetch user mood profile" });
    }
  });

  // Update supporter status
  app.post("/api/user/supporter", isAuthenticated, async (req: any, res) => {
    try {
      const { isSupporter } = req.body;
      await storage.updateUserSupporterStatus(req.user.id, isSupporter);
      res.json({ success: true, message: "Supporter status updated" });
    } catch (error) {
      console.error("Error updating supporter status:", error);
      res.status(500).json({ message: "Failed to update supporter status" });
    }
  });

  // AI insights
  app.get("/api/ai-insights", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const insights = await storage.getAIInsights(userId);
      res.json(insights);
    } catch (error) {
      console.error("Error fetching AI insights:", error);
      res.status(500).json({ message: "Failed to fetch AI insights" });
    }
  });

  // User patterns endpoint
  app.get("/api/user/patterns", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const patterns = await storage.getUserWatchingPatterns(userId);
      res.json(patterns);
    } catch (error) {
      console.error("Error fetching user patterns:", error);
      res.status(500).json({ message: "Failed to fetch user patterns" });
    }
  });

  // Friend system routes
  app.post("/api/friends/request", isAuthenticated, async (req: any, res) => {
    try {
      const requesterId = req.user.id;
      const { receiverId } = req.body;
      const friendship = await storage.sendFriendRequest(
        requesterId,
        receiverId
      );
      res.json(friendship);
    } catch (error) {
      console.error("Error sending friend request:", error);
      res.status(500).json({ message: "Failed to send friend request" });
    }
  });

  app.post(
    "/api/friends/accept/:friendshipId",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const friendshipId = parseInt(req.params.friendshipId);
        const friendship = await storage.acceptFriendRequest(friendshipId);
        res.json(friendship);
      } catch (error) {
        console.error("Error accepting friend request:", error);
        res.status(500).json({ message: "Failed to accept friend request" });
      }
    }
  );

  app.delete(
    "/api/friends/reject/:friendshipId",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const friendshipId = parseInt(req.params.friendshipId);
        await storage.rejectFriendRequest(friendshipId);
        res.json({ success: true });
      } catch (error) {
        console.error("Error rejecting friend request:", error);
        res.status(500).json({ message: "Failed to reject friend request" });
      }
    }
  );

  app.get("/api/friends", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const friends = await storage.getFriends(userId);
      res.json(friends);
    } catch (error) {
      console.error("Error fetching friends:", error);
      res.status(500).json({ message: "Failed to fetch friends" });
    }
  });

  app.get("/api/friends/requests", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const requests = await storage.getFriendRequests(userId);
      res.json(requests);
    } catch (error) {
      console.error("Error fetching friend requests:", error);
      res.status(500).json({ message: "Failed to fetch friend requests" });
    }
  });

  app.delete(
    "/api/friends/:friendId",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const userId = req.user.id;
        const { friendId } = req.params;
        await storage.unfriend(userId, friendId);
        res.json({ success: true });
      } catch (error) {
        console.error("Error unfriending:", error);
        res.status(500).json({ message: "Failed to unfriend" });
      }
    }
  );

  // Friend invite routes
  app.post("/api/friends/invite", isAuthenticated, async (req: any, res) => {
    try {
      const fromUserId = req.user.id;
      const { toEmail } = req.body;


      // Get sender's info
      const fromUser = await storage.getUser(fromUserId);
      if (!fromUser) {
        return res.status(404).json({ message: "User not found" });
      }

      const invite = await storage.sendFriendInvite(fromUserId, toEmail);

      // Send email using SendGrid (with error handling)
      let emailSent = false;
      try {
        const { sendFriendInviteEmail } = await import("./email");
        const fromUserName = fromUser.firstName || fromUser.email || "A friend";
        const domain = req.hostname === "localhost" ? "localhost:3000" : req.hostname;
        emailSent = await sendFriendInviteEmail(
          toEmail,
          fromUserName,
          invite.inviteToken,
          domain
        );
      } catch (emailError) {
        console.error("Email sending failed:", emailError);
        emailSent = false;
      }

      res.json({
        ...invite,
        message: emailSent
          ? "Friend invite sent successfully!"
          : "Invite created but email delivery failed",
        emailSent,
      });
    } catch (error) {
      console.error("Error sending friend invite:", error);
      res.status(500).json({ message: "Failed to send friend invite" });
    }
  });

  app.get("/api/friends/invite/:inviteToken", async (req, res) => {
    try {
      const { inviteToken } = req.params;
      const [invite] = await db
        .select({
          id: friendInvites.id,
          fromUserId: friendInvites.fromUserId,
          toEmail: friendInvites.toEmail,
          status: friendInvites.status,
          expiresAt: friendInvites.expiresAt,
          fromUser: {
            id: users.id,
            firstName: users.firstName,
            lastName: users.lastName,
            email: users.email,
            profileImageUrl: users.profileImageUrl,
          },
        })
        .from(friendInvites)
        .innerJoin(users, eq(friendInvites.fromUserId, users.id))
        .where(eq(friendInvites.inviteToken, inviteToken))
        .limit(1);

      if (!invite) {
        return res.status(404).json({ message: "Invite not found" });
      }

      if (invite.status !== "pending" || invite.expiresAt < new Date()) {
        return res
          .status(400)
          .json({ message: "Invite expired or already used" });
      }

      res.json(invite);
    } catch (error) {
      console.error("Error fetching friend invite:", error);
      res.status(500).json({ message: "Failed to fetch friend invite" });
    }
  });

  app.post(
    "/api/friends/invite/accept",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const userId = req.user.id;
        const { inviteToken } = req.body;
          // Accepting friend invite

        const friendship = await storage.acceptFriendInvite(
          inviteToken,
          userId
        );

        res.json(friendship);
      } catch (error) {
        console.error("Error accepting friend invite:", error);
        res.status(500).json({ message: "Failed to accept friend invite" });
      }
    }
  );

  app.get("/api/friends/invites", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const invites = await storage.getFriendInvites(userId);
      res.json(invites);
    } catch (error) {
      console.error("Error fetching friend invites:", error);
      res.status(500).json({ message: "Failed to fetch friend invites" });
    }
  });

  // Get friend invites for current user (received invites)
  app.get("/api/friends/invites-for-me", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const currentUser = await storage.getUser(userId);
      
      if (!currentUser) {
        return res.status(404).json({ message: "User not found" });
      }
      
      const userEmail = currentUser.email;
      
      // Get invites sent to this user's email
      const invites = await db
        .select({
          id: friendInvites.id,
          inviteToken: friendInvites.inviteToken,
          fromUserId: friendInvites.fromUserId,
          toEmail: friendInvites.toEmail,
          status: friendInvites.status,
          createdAt: friendInvites.createdAt,
          expiresAt: friendInvites.expiresAt,
          fromUserName: sql<string>`concat(coalesce(${users.firstName}, ''), ' ', coalesce(${users.lastName}, ''))`,
          fromUserEmail: users.email
        })
        .from(friendInvites)
        .innerJoin(users, eq(friendInvites.fromUserId, users.id))
        .where(
          and(
            eq(friendInvites.toEmail, userEmail),
            eq(friendInvites.status, "pending"),
            gte(friendInvites.expiresAt, new Date())
          )
        )
        .orderBy(desc(friendInvites.createdAt));
      
      res.json(invites);
    } catch (error) {
      console.error("Error fetching friend invites for me:", error);
      res.status(500).json({ message: "Failed to fetch friend invites" });
    }
  });

  // Cancel/Delete friend invite
  app.delete(
    "/api/friends/invite/:inviteId",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const userId = req.user.id;
        const inviteId = parseInt(req.params.inviteId);


        // Check if invite exists and belongs to user
        const invite = await db
          .select()
          .from(friendInvites)
          .where(eq(friendInvites.id, inviteId))
          .limit(1);

        if (invite.length === 0) {
          return res.status(404).json({ message: "Invite not found" });
        }

        if (invite[0].fromUserId !== userId) {
          return res.status(403).json({ message: "Access denied" });
        }

        // Delete the invite
        await db.delete(friendInvites).where(eq(friendInvites.id, inviteId));
        
        res.json({ success: true, message: "Invite deleted successfully" });
      } catch (error) {
        console.error("Error deleting friend invite:", error);
        res.status(500).json({ message: "Failed to delete friend invite" });
      }
    }
  );

  // Contact form route
  app.post("/api/contact", async (req, res) => {
    try {
      const { name, email, subject, message, category } = req.body;

      // Basic validation
      if (!name || !email || !subject || !message) {
        return res.status(400).json({ message: "All fields are required" });
      }

      // Email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return res.status(400).json({ message: "Invalid email address" });
      }

      // Contact form data processed

      // Send email using SendGrid
      const { sendContactEmail } = await import("./email");
      const emailSent = await sendContactEmail({
        name,
        email,
        subject,
        message,
        category: category || "general",
      });

      if (emailSent) {
        res.json({
          success: true,
          message:
            "Your message has been sent successfully! We'll get back to you within 24 hours.",
        });
      } else {
        res.status(500).json({
          success: false,
          message:
            "Failed to send message. Please try again or email us <NAME_EMAIL>",
        });
      }
    } catch (error) {
      console.error("Error processing contact form:", error);
      res.status(500).json({
        success: false,
        message: "Failed to send message. Please try again later.",
      });
    }
  });

  // Learning path routes
  app.post("/api/learning-paths", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const { planId } = req.body;

      if (!planId) {
        return res.status(400).json({ message: "Plan ID is required" });
      }

      // Check if plan exists and user has access
      const userPlans = await storage.getUserLearningPlans(userId);
      const plan = userPlans.find((p) => p.id === parseInt(planId));
      if (!plan) {
        return res
          .status(404)
          .json({ message: "Learning plan not found or access denied" });
      }

      // Check if learning path already exists
      const existingPath = await storage.getLearningPath(userId, parseInt(planId));
      if (existingPath) {
        return res.json(existingPath);
      }

      const path = await storage.createLearningPath(userId, parseInt(planId));
      res.status(201).json(path);
    } catch (error) {
      console.error("Error creating learning path:", error);
      res.status(500).json({ 
        message: "Failed to create learning path",
        error: error instanceof Error ? error.message : "Unknown error"
      });
    }
  });

  app.get(
    "/api/learning-paths/:planId",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const userId = req.user.id;
        const planId = parseInt(req.params.planId);
          // Fetching learning path

        const path = await storage.getLearningPath(userId, planId);
        if (!path) {
          return res.status(404).json({ message: "Learning path not found" });
        }

        // Get path nodes
        const nodes = await storage.getPathNodes(path.id);

        const pathWithNodes = {
          ...path,
          nodes: nodes,
        };

        res.json(pathWithNodes);
      } catch (error) {
        console.error("Error fetching learning path:", error);
        res.status(500).json({ message: "Failed to fetch learning path" });
      }
    }
  );

  app.put(
    "/api/learning-paths/:pathId",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const pathId = parseInt(req.params.pathId);
        const { pathData, completionPercentage } = req.body;
        const path = await storage.updateLearningPath(
          pathId,
          pathData,
          completionPercentage
        );
        res.json(path);
      } catch (error) {
        console.error("Error updating learning path:", error);
        res.status(500).json({ message: "Failed to update learning path" });
      }
    }
  );

  app.put(
    "/api/learning-paths/:pathId/notes",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const pathId = parseInt(req.params.pathId);
        const { notes } = req.body;
        const path = await storage.updateLearningPathNotes(pathId, notes);
        res.json(path);
      } catch (error) {
        console.error("Error updating learning path notes:", error);
        res
          .status(500)
          .json({ message: "Failed to update learning path notes" });
      }
    }
  );

  app.get(
    "/api/learning-paths/:pathId/nodes",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const pathId = parseInt(req.params.pathId);
        const nodes = await storage.getPathNodes(pathId);
        res.json(nodes);
      } catch (error) {
        console.error("Error fetching path nodes:", error);
        res.status(500).json({ message: "Failed to fetch path nodes" });
      }
    }
  );

  app.put(
    "/api/learning-paths/nodes/:nodeId",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const nodeId = parseInt(req.params.nodeId);
        const { isCompleted } = req.body;
        const node = await storage.updatePathNode(nodeId, isCompleted);
        res.json(node);
      } catch (error) {
        console.error("Error updating path node:", error);
        res.status(500).json({ message: "Failed to update path node" });
      }
    }
  );

  // Study group routes
  app.post("/api/study-groups", isAuthenticated, async (req: any, res) => {
    try {
      const createdById = req.user.id;
      const groupData = { ...req.body, createdById };
      const group = await storage.createStudyGroup(groupData);
      res.json(group);
    } catch (error) {
      console.error("Error creating study group:", error);
      res.status(500).json({ message: "Failed to create study group" });
    }
  });

  app.get(
    "/api/study-groups/:groupId",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const groupId = parseInt(req.params.groupId);
        const group = await storage.getStudyGroup(groupId);
        if (!group) {
          return res.status(404).json({ message: "Study group not found" });
        }
        res.json(group);
      } catch (error) {
        console.error("Error fetching study group:", error);
        res.status(500).json({ message: "Failed to fetch study group" });
      }
    }
  );

  app.get("/api/study-groups", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const groups = await storage.getUserStudyGroups(userId);
      res.json(groups);
    } catch (error) {
      console.error("Error fetching study groups:", error);
      res.status(500).json({ message: "Failed to fetch study groups" });
    }
  });

  app.post(
    "/api/study-groups/:groupId/join",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const groupId = parseInt(req.params.groupId);
        const userId = req.user.id;
        const member = await storage.joinStudyGroup(groupId, userId);
        res.json(member);
      } catch (error) {
        console.error("Error joining study group:", error);
        res.status(500).json({ message: "Failed to join study group" });
      }
    }
  );

  app.delete(
    "/api/study-groups/:groupId/leave",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const groupId = parseInt(req.params.groupId);
        const userId = req.user.id;
        await storage.leaveStudyGroup(groupId, userId);
        res.json({ success: true });
      } catch (error) {
        console.error("Error leaving study group:", error);
        res.status(500).json({ message: "Failed to leave study group" });
      }
    }
  );

  app.get(
    "/api/study-groups/:groupId/members",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const groupId = parseInt(req.params.groupId);
        const members = await storage.getStudyGroupMembers(groupId);
        res.json(members);
      } catch (error) {
        console.error("Error fetching group members:", error);
        res.status(500).json({ message: "Failed to fetch group members" });
      }
    }
  );

  app.get(
    "/api/study-groups/:groupId/activities",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const groupId = parseInt(req.params.groupId);
        const activities = await storage.getGroupActivities(groupId);
        res.json(activities);
      } catch (error) {
        console.error("Error fetching group activities:", error);
        res.status(500).json({ message: "Failed to fetch group activities" });
      }
    }
  );

  // AI Difficulty Optimizer routes
  app.get(
    "/api/difficulty/analyze/:videoId",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const videoId = parseInt(req.params.videoId);
        const analysis = await storage.analyzeVideoDifficulty(videoId);
        res.json(analysis);
      } catch (error) {
        console.error("Error analyzing video difficulty:", error);
        res.status(500).json({ message: "Failed to analyze video difficulty" });
      }
    }
  );

  app.get("/api/user/skill-profile", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const { subject } = req.query;
      const profiles = await storage.getUserSkillProfile(
        userId,
        subject as string
      );
      res.json(profiles);
    } catch (error) {
      console.error("Error fetching skill profile:", error);
      res.status(500).json({ message: "Failed to fetch skill profile" });
    }
  });

  app.post(
    "/api/user/skill-profile",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const userId = req.user.id;
        const { subject, ...profileData } = req.body;
        const profile = await storage.updateUserSkillProfile(
          userId,
          subject,
          profileData
        );
        res.json(profile);
      } catch (error) {
        console.error("Error updating skill profile:", error);
        res.status(500).json({ message: "Failed to update skill profile" });
      }
    }
  );

  app.get(
    "/api/recommendations/optimized/:planId",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const userId = req.user.id;
        const planId = parseInt(req.params.planId);
        const limit = parseInt(req.query.limit as string) || 10;

        const recommendations = await storage.getOptimizedVideoRecommendations(
          userId,
          planId,
          limit
        );
        res.json(recommendations);
      } catch (error) {
        console.error("Error fetching optimized recommendations:", error);
        res
          .status(500)
          .json({ message: "Failed to fetch optimized recommendations" });
      }
    }
  );

  app.post(
    "/api/learning/adaptation",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const userId = req.user.id;
        const adaptationData = { ...req.body, userId };
        const adaptation = await storage.recordLearningAdaptation(
          adaptationData
        );
        res.json(adaptation);
      } catch (error) {
        console.error("Error recording learning adaptation:", error);
        res
          .status(500)
          .json({ message: "Failed to record learning adaptation" });
      }
    }
  );

  app.get(
    "/api/learning/progression/:subject",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const userId = req.user.id;
        const subject = req.params.subject;
        const path = await storage.getDifficultyProgressionPath(
          userId,
          subject
        );
        res.json(path);
      } catch (error) {
        console.error("Error fetching difficulty progression:", error);
        res
          .status(500)
          .json({ message: "Failed to fetch difficulty progression" });
      }
    }
  );

  app.get(
    "/api/learning/struggle-analysis",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const userId = req.user.id;
        const analysis = await storage.analyzeUserStrugglePatterns(userId);
        res.json(analysis);
      } catch (error) {
        console.error("Error analyzing struggle patterns:", error);
        res
          .status(500)
          .json({ message: "Failed to analyze struggle patterns" });
      }
    }
  );

  app.get(
    "/api/learning/adaptive-insights",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const userId = req.user.id;
        const insights = await storage.getAdaptiveLearningInsights(userId);
        res.json(insights);
      } catch (error) {
        console.error("Error fetching adaptive insights:", error);
        res.status(500).json({ message: "Failed to fetch adaptive insights" });
      }
    }
  );

  // Debug endpoint to check plan details
  app.get("/api/debug/plan/:slug", isAuthenticated, async (req: any, res) => {
    try {
      const { slug } = req.params;
      const userId = req.user.id;
      
      const plan = await storage.getLearningPlanBySlug(slug);
      if (!plan) {
        return res.status(404).json({ message: "Plan not found" });
      }
      
      const permissions = await storage.canUserAccessPlan(plan.id, userId);
      
      res.json({
        plan: {
          id: plan.id,
          title: plan.title,
          slug: plan.slug,
          userId: plan.userId,
          isPublic: plan.isPublic,
          isActive: plan.isActive
        },
        currentUser: userId,
        permissions,
        isOwner: plan.userId === userId
      });
    } catch (error) {
      console.error("Debug plan error:", error);
      res.status(500).json({ error: error.message });
    }
  });

  // Debug endpoint to check video stats
  app.get("/api/debug/videos", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      
      // Get total videos in database
      const totalVideosResult = await db.select({ count: sql<number>`count(*)` }).from(videos);
      const totalVideos = totalVideosResult[0]?.count || 0;
      
      // Get user's watched videos
      const watchedVideosResult = await db
        .select({ count: sql<number>`count(*)` })
        .from(videoProgress)
        .where(eq(videoProgress.userId, userId));
      const watchedVideos = watchedVideosResult[0]?.count || 0;
      
      // Get user's completed videos
      const completedVideosResult = await db
        .select({ count: sql<number>`count(*)` })
        .from(videoProgress)
        .where(and(eq(videoProgress.userId, userId), eq(videoProgress.isCompleted, true)));
      const completedVideos = completedVideosResult[0]?.count || 0;
      
      res.json({
        userId,
        totalVideos,
        watchedVideos,
        completedVideos,
        availableForRecommendations: Math.max(0, totalVideos - watchedVideos)
      });
    } catch (error) {
      console.error("Debug videos error:", error);
      res.status(500).json({ error: error.message });
    }
  });

  // Debug endpoint to check progress data
  app.get("/api/debug/progress/:planId", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const planId = parseInt(req.params.planId);
      
      // Get plan videos
      const planVideos = await db
        .select({
          id: planVideos.id,
          videoId: planVideos.videoId,
          orderIndex: planVideos.orderIndex,
          video: {
            id: videos.id,
            title: videos.title,
            youtubeId: videos.youtubeId
          }
        })
        .from(planVideos)
        .innerJoin(videos, eq(planVideos.videoId, videos.id))
        .where(eq(planVideos.planId, planId))
        .orderBy(planVideos.orderIndex);
      
      // Get user progress for these videos
      const progressData = await db
        .select()
        .from(videoProgress)
        .where(
          and(
            eq(videoProgress.userId, userId),
            inArray(videoProgress.videoId, planVideos.map(pv => pv.videoId))
          )
        );
      
      // Get all progress for this user (for comparison)
      const allUserProgress = await db
        .select()
        .from(videoProgress)
        .where(eq(videoProgress.userId, userId));
      
      res.json({
        userId,
        planId,
        planVideos: planVideos.length,
        planVideosList: planVideos,
        progressRecords: progressData.length,
        progressData,
        allUserProgressCount: allUserProgress.length,
        completedCount: progressData.filter(p => p.isCompleted).length
      });
    } catch (error) {
      console.error("Debug progress error:", error);
      res.status(500).json({ error: error.message });
    }
  });

  // Debug endpoint to check all data
  app.get("/api/debug/notifications", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims?.sub || req.user.id;
      const currentUser = await storage.getUser(userId);
      
      // Get all friend invites in database
      const allInvites = await db.select().from(friendInvites);
      
      // Get all friendships
      const allFriendships = await db.select().from(friendships);
      
      // Get all users
      const allUsers = await db.select().from(users).limit(10);
      
      res.json({
        currentUser: {
          id: userId,
          user: currentUser,
          reqUser: req.user
        },
        allInvites,
        allFriendships,
        allUsers: allUsers.map(u => ({ id: u.id, email: u.email, firstName: u.firstName, lastName: u.lastName }))
      });
    } catch (error) {
      console.error("Debug error:", error);
      res.status(500).json({ error: error.message });
    }
  });

  // Notifications routes
  app.get("/api/notifications", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims?.sub || req.user.id;
      
      
      // Get current user's email from database
      const currentUser = await storage.getUser(userId);
      
      if (!currentUser) {
        return res.status(404).json({ message: "User not found" });
      }
      
      const userEmail = currentUser.email;
      
      const notifications = [];
      
      // Get friend requests (direct user-to-user requests)
      const friendRequests = await db
        .select()
        .from(friendships)
        .innerJoin(users, eq(friendships.requesterId, users.id))
        .where(
          and(
            eq(friendships.receiverId, userId),
            eq(friendships.status, "pending")
          )
        );
      
      
      // Get friend invites (email-based invitations)
      const allInvites = await db.select().from(friendInvites);
      
      const userInvites = await db
        .select()
        .from(friendInvites)
        .innerJoin(users, eq(friendInvites.fromUserId, users.id))
        .where(
          and(
            eq(friendInvites.toEmail, userEmail),
            eq(friendInvites.status, "pending")
          )
        );
      
      
      // Get plan share notifications
      let planSharesData = [];
      try {
        planSharesData = await db
          .select({
            id: planShares.id,
            planId: planShares.planId,
            sharedByUserId: planShares.sharedByUserId,
            createdAt: planShares.createdAt,
            plan: {
              title: learningPlans.title
            },
            fromUser: {
              firstName: users.firstName,
              lastName: users.lastName,
              email: users.email
            }
          })
          .from(planShares)
          .innerJoin(users, eq(planShares.sharedByUserId, users.id))
          .innerJoin(learningPlans, eq(planShares.planId, learningPlans.id))
          .where(eq(planShares.sharedWithUserId, userId));
      } catch (error) {
        planSharesData = [];
      }
      
      
      // Format notifications
      const formattedRequests = friendRequests.map(item => ({
        id: item.friendships.id,
        type: 'friend_request',
        title: 'New Friend Request',
        message: `${item.users.firstName || ''} ${item.users.lastName || ''} (${item.users.email}) wants to connect with you`,
        isRead: false,
        createdAt: item.friendships.createdAt,
        data: {
          requestId: item.friendships.id,
          fromUserId: item.friendships.requesterId,
          fromUserName: `${item.users.firstName || ''} ${item.users.lastName || ''}`.trim(),
          fromUserEmail: item.users.email
        }
      }));
      
      const formattedInvites = userInvites.map(item => ({
        id: item.friend_invites?.id || item.friendInvites?.id,
        type: 'friend_invite',
        title: 'Friend Invitation',
        message: `${item.users.firstName || ''} ${item.users.lastName || ''} (${item.users.email}) invited you to join Learniify`,
        isRead: false,
        createdAt: item.friend_invites?.createdAt || item.friendInvites?.createdAt,
        data: {
          inviteId: item.friend_invites?.id || item.friendInvites?.id,
          inviteToken: item.friend_invites?.inviteToken || item.friendInvites?.inviteToken,
          fromUserId: item.friend_invites?.fromUserId || item.friendInvites?.fromUserId,
          fromUserName: `${item.users.firstName || ''} ${item.users.lastName || ''}`.trim(),
          fromUserEmail: item.users.email
        }
      }));
      
      const formattedPlanShares = (planSharesData || []).map(item => ({
        id: item.id + 10000, // Offset to avoid ID conflicts
        type: 'plan_share',
        title: 'Learning Plan Shared',
        message: `${item.fromUser.firstName || ''} ${item.fromUser.lastName || ''} shared "${item.plan.title}" with you`,
        isRead: false,
        createdAt: item.createdAt,
        data: {
          shareId: item.id,
          planId: item.planId,
          planTitle: item.plan.title,
          fromUserId: item.sharedByUserId,
          fromUserName: `${item.fromUser.firstName || ''} ${item.fromUser.lastName || ''}`.trim(),
          fromUserEmail: item.fromUser.email
        }
      }));
      
      notifications.push(...formattedRequests, ...formattedInvites, ...formattedPlanShares);
      
      // Sort by creation date
      notifications.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
      
      
      res.json(notifications);
    } catch (error) {
      console.error("❌ Error fetching notifications:", error);
      res.status(500).json({ message: "Failed to fetch notifications", error: error.message });
    }
  });

  app.put("/api/notifications/:id/read", isAuthenticated, async (req: any, res) => {
    try {
      // Temporary implementation
      res.json({ success: true });
    } catch (error) {
      console.error("Error marking notification as read:", error);
      res.status(500).json({ message: "Failed to mark notification as read" });
    }
  });

  app.put("/api/notifications/mark-all-read", isAuthenticated, async (req: any, res) => {
    try {
      // Temporary implementation
      res.json({ success: true });
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
      res.status(500).json({ message: "Failed to mark all notifications as read" });
    }
  });

  // User profile routes
  app.get("/api/user/profile", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }
      res.json(user);
    } catch (error) {
      console.error("Error fetching user profile:", error);
      res.status(500).json({ message: "Failed to fetch user profile" });
    }
  });

  app.put("/api/user/profile", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const { firstName, lastName } = req.body;
      const updatedUser = await storage.updateUserProfile(userId, { firstName, lastName });
      res.json(updatedUser);
    } catch (error) {
      console.error("Error updating user profile:", error);
      res.status(500).json({ message: "Failed to update user profile" });
    }
  });

  app.delete("/api/user/account", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      await storage.deleteUserAccount(userId);
      res.json({ message: "Account deleted successfully" });
    } catch (error) {
      console.error("Error deleting user account:", error);
      res.status(500).json({ message: "Failed to delete user account" });
    }
  });

  app.get("/api/user/viewing-history", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const history = await storage.getUserViewingHistory(userId, 10);
      res.json(history);
    } catch (error) {
      console.error("Error fetching viewing history:", error);
      res.status(500).json({ message: "Failed to fetch viewing history" });
    }
  });

  // Common Playlist routes
  app.get("/api/common-playlist", isAuthenticated, async (req: any, res) => {
    try {
      const category = req.query.category as string;
      const playlist = await storage.getCommonPlaylist(category);
      res.json(playlist);
    } catch (error) {
      console.error("Error fetching common playlist:", error);
      res.status(500).json({ message: "Failed to fetch common playlist" });
    }
  });

  app.post("/api/common-playlist", isAuthenticated, async (req: any, res) => {
    try {
      const item = await storage.addToCommonPlaylist(req.body);
      res.json(item);
    } catch (error) {
      console.error("Error adding to common playlist:", error);
      res.status(500).json({ message: "Failed to add to common playlist" });
    }
  });

  app.get(
    "/api/user-playlist-selections",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const userId = req.user.id;
        const selections = await storage.getUserPlaylistSelections(userId);
        res.json(selections);
      } catch (error) {
        console.error("Error fetching user playlist selections:", error);
        res
          .status(500)
          .json({ message: "Failed to fetch user playlist selections" });
      }
    }
  );

  app.post(
    "/api/user-playlist-selections",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const userId = req.user.id;
        const selection = await storage.addUserPlaylistSelection({
          ...req.body,
          userId,
        });
        res.json(selection);
      } catch (error) {
        console.error("Error adding user playlist selection:", error);
        res
          .status(500)
          .json({ message: "Failed to add user playlist selection" });
      }
    }
  );

  app.delete(
    "/api/user-playlist-selections/:playlistItemId",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const userId = req.user.id;
        const playlistItemId = parseInt(req.params.playlistItemId);
        await storage.removeUserPlaylistSelection(userId, playlistItemId);
        res.json({ success: true });
      } catch (error) {
        console.error("Error removing user playlist selection:", error);
        res
          .status(500)
          .json({ message: "Failed to remove user playlist selection" });
      }
    }
  );

  app.post(
    "/api/plans/from-playlist",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const userId = req.user.id;
        const { title, playlistItemIds } = req.body;
        const plan = await storage.createPlanFromPlaylistItems(
          userId,
          title,
          playlistItemIds
        );
        res.json(plan);
      } catch (error) {
        console.error("Error creating plan from playlist:", error);
        res
          .status(500)
          .json({ message: "Failed to create plan from playlist" });
      }
    }
  );

  app.post(
    "/api/plans/:planId/add-playlist-item",
    isAuthenticated,
    async (req: any, res) => {
      try {
        const userId = req.user.id;
        const planId = parseInt(req.params.planId);
        const { playlistItemId } = req.body;
        
        
        await storage.addPlaylistItemToPlan(userId, playlistItemId, planId);
        
        res.json({ success: true, message: 'Playlist item added to plan successfully' });
      } catch (error) {
        console.error("❌ Error adding playlist item to plan:", error);
        res
          .status(500)
          .json({ 
            message: "Failed to add playlist item to plan",
            error: error instanceof Error ? error.message : 'Unknown error'
          });
      }
    }
  );

  // Public plans routes
  app.get("/api/public-plans", async (req, res) => {
    try {
      const { page = 1, limit = 12, search } = req.query;
      const offset = (parseInt(page as string) - 1) * parseInt(limit as string);
      
      let query = db
        .select({
          id: learningPlans.id,
          title: learningPlans.title,
          description: learningPlans.description,
          slug: learningPlans.slug,
          createdAt: learningPlans.createdAt,
          userId: learningPlans.userId,
          author: {
            firstName: users.firstName,
            lastName: users.lastName,
            email: users.email
          }
        })
        .from(learningPlans)
        .innerJoin(users, eq(learningPlans.userId, users.id))
        .where(
          and(
            eq(learningPlans.isPublic, true),
            eq(learningPlans.isActive, true) // Only show active plans
          )
        )
        .orderBy(desc(learningPlans.createdAt))
        .limit(parseInt(limit as string))
        .offset(offset);
      
      if (search) {
        query = query.where(
          and(
            eq(learningPlans.isPublic, true),
            eq(learningPlans.isActive, true),
            or(
              sql`${learningPlans.title} ILIKE ${`%${search}%`}`,
              sql`${learningPlans.description} ILIKE ${`%${search}%`}`
            )
          )
        );
      }
      
      const publicPlans = await query;
      res.json(publicPlans);
    } catch (error) {
      console.error("Error fetching public plans:", error);
      res.status(500).json({ message: "Failed to fetch public plans" });
    }
  });

  // Add public plan to user's library
  app.post("/api/shared-plan/public/:id/accept", isAuthenticated, async (req: any, res) => {
    try {
      const planId = parseInt(req.params.id);
      const userId = req.user.id;
      
      // Get original plan
      const [originalPlan] = await db
        .select()
        .from(learningPlans)
        .where(and(eq(learningPlans.id, planId), eq(learningPlans.isPublic, true)))
        .limit(1);
      
      if (!originalPlan) {
        return res.status(404).json({ message: "Public plan not found" });
      }
      
      // Create copy
      const slug = await storage.generatePlanSlug(originalPlan.title);
      const newPlan = await storage.createLearningPlan({
        userId,
        title: originalPlan.title,
        description: originalPlan.description || '',
        slug,
        isActive: true,
        isPublic: false
      });
      
      // Copy videos
      const originalVideos = await storage.getPlanVideos(planId);
      for (const video of originalVideos) {
        await storage.addVideoToPlan({
          planId: newPlan.id,
          videoId: video.videoId,
          orderIndex: video.orderIndex
        });
      }
      
      res.json(newPlan);
    } catch (error) {
      console.error("Error adding plan to library:", error);
      res.status(500).json({ message: "Failed to add plan to library" });
    }
  });

  // Make plan public/private
  app.put("/api/learning-plans/:id/visibility", isAuthenticated, async (req: any, res) => {
    try {
      const planId = parseInt(req.params.id);
      const userId = req.user.id;
      const { isPublic } = req.body;
      
      const [plan] = await db
        .select()
        .from(learningPlans)
        .where(and(eq(learningPlans.id, planId), eq(learningPlans.userId, userId)))
        .limit(1);
      
      if (!plan) {
        return res.status(404).json({ message: "Plan not found" });
      }
      
      await db
        .update(learningPlans)
        .set({ isPublic, updatedAt: new Date() })
        .where(eq(learningPlans.id, planId));
      
      res.json({ success: true, isPublic });
    } catch (error) {
      console.error("Error updating plan visibility:", error);
      res.status(500).json({ message: "Failed to update plan visibility" });
    }
  });

  // Favorites routes
  app.post("/api/favorites/videos/:videoId", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const videoId = parseInt(req.params.videoId);
      
      // Check if video exists
      const video = await storage.getVideo(videoId);
      if (!video) {
        return res.status(404).json({ message: "Video not found" });
      }
      
      const favorite = await storage.addVideoToFavorites(userId, videoId);
      res.json({ success: true, favorite });
    } catch (error) {
      console.error("Error adding video to favorites:", error);
      res.status(500).json({ message: "Failed to add video to favorites" });
    }
  });

  app.delete("/api/favorites/videos/:videoId", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const videoId = parseInt(req.params.videoId);
      
      await storage.removeVideoFromFavorites(userId, videoId);
      res.json({ success: true });
    } catch (error) {
      console.error("Error removing video from favorites:", error);
      res.status(500).json({ message: "Failed to remove video from favorites" });
    }
  });

  app.get("/api/favorites/videos", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const favoriteVideos = await storage.getUserFavoriteVideos(userId);
      res.json(favoriteVideos);
    } catch (error) {
      console.error("Error fetching favorite videos:", error);
      res.status(500).json({ message: "Failed to fetch favorite videos" });
    }
  });

  app.get("/api/favorites/videos/:videoId/status", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const videoId = parseInt(req.params.videoId);
      
      const isFavorited = await storage.isVideoFavorited(userId, videoId);
      res.json({ isFavorited });
    } catch (error) {
      console.error("Error checking video favorite status:", error);
      res.status(500).json({ message: "Failed to check favorite status" });
    }
  });

  app.post("/api/favorites/playlists/:playlistId", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const playlistId = parseInt(req.params.playlistId);
      
      // Check if playlist exists and is accessible
      const permissions = await storage.canUserAccessPlan(playlistId, userId);
      if (!permissions.canView) {
        return res.status(403).json({ message: "Access denied to this playlist" });
      }
      
      const favorite = await storage.addPlaylistToFavorites(userId, playlistId);
      res.json({ success: true, favorite });
    } catch (error) {
      console.error("Error adding playlist to favorites:", error);
      res.status(500).json({ message: "Failed to add playlist to favorites" });
    }
  });

  app.delete("/api/favorites/playlists/:playlistId", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const playlistId = parseInt(req.params.playlistId);
      
      await storage.removePlaylistFromFavorites(userId, playlistId);
      res.json({ success: true });
    } catch (error) {
      console.error("Error removing playlist from favorites:", error);
      res.status(500).json({ message: "Failed to remove playlist from favorites" });
    }
  });

  app.get("/api/favorites/playlists", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const favoritePlaylists = await storage.getUserFavoritePlaylists(userId);
      res.json(favoritePlaylists);
    } catch (error) {
      console.error("Error fetching favorite playlists:", error);
      res.status(500).json({ message: "Failed to fetch favorite playlists" });
    }
  });

  app.get("/api/favorites/playlists/:playlistId/status", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const playlistId = parseInt(req.params.playlistId);
      
      const isFavorited = await storage.isPlaylistFavorited(userId, playlistId);
      res.json({ isFavorited });
    } catch (error) {
      console.error("Error checking playlist favorite status:", error);
      res.status(500).json({ message: "Failed to check favorite status" });
    }
  });

  // Plan likes routes
  app.post("/api/plans/:planId/like", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const planId = parseInt(req.params.planId);
      
      const like = await storage.addPlanLike(userId, planId);
      const totalLikes = await storage.getPlanLikes(planId);
      
      res.json({ success: true, like, totalLikes });
    } catch (error) {
      console.error("Error liking plan:", error);
      res.status(500).json({ message: "Failed to like plan" });
    }
  });

  app.delete("/api/plans/:planId/like", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const planId = parseInt(req.params.planId);
      
      await storage.removePlanLike(userId, planId);
      const totalLikes = await storage.getPlanLikes(planId);
      
      res.json({ success: true, totalLikes });
    } catch (error) {
      console.error("Error unliking plan:", error);
      res.status(500).json({ message: "Failed to unlike plan" });
    }
  });

  app.get("/api/plans/:planId/likes", async (req, res) => {
    try {
      const planId = parseInt(req.params.planId);
      const totalLikes = await storage.getPlanLikes(planId);
      
      res.json({ totalLikes });
    } catch (error) {
      console.error("Error getting plan likes:", error);
      res.status(500).json({ message: "Failed to get plan likes" });
    }
  });

  app.get("/api/plans/:planId/like-status", isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.id;
      const planId = parseInt(req.params.planId);
      
      const isLiked = await storage.isPlanLiked(userId, planId);
      res.json({ isLiked });
    } catch (error) {
      console.error("Error checking plan like status:", error);
      res.status(500).json({ message: "Failed to check like status" });
    }
  });

  // Active Users Tracking (in-memory for real-time tracking)
  const activeUsers = new Set<string>();
  const userLastActivity = new Map<string, Date>();
  const userRegions = new Map<string, string>();

  // Clean up inactive users every 5 minutes
  setInterval(() => {
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    for (const [userId, lastActivity] of userLastActivity.entries()) {
      if (lastActivity < fiveMinutesAgo) {
        activeUsers.delete(userId);
        userLastActivity.delete(userId);
      }
    }
  }, 60000); // Check every minute

  // Get user region from IP
  const getUserRegion = async (ip: string): Promise<string> => {
    try {
      // Skip localhost/private IPs
      if (ip === '127.0.0.1' || ip === '::1' || ip.startsWith('192.168.') || ip.startsWith('10.')) {
        return 'Local';
      }
      
      const response = await fetch(`http://ip-api.com/json/${ip}?fields=country,regionName`);
      const data = await response.json();
      return data.country || 'Unknown';
    } catch (error) {
      return 'Unknown';
    }
  };

  // Track user activity with region
  const trackUserActivity = async (userId: string, req?: any) => {
    activeUsers.add(userId);
    userLastActivity.set(userId, new Date());
    
    if (req && !userRegions.has(userId)) {
      const ip = req.ip || req.connection.remoteAddress || req.headers['x-forwarded-for']?.split(',')[0];
      const region = await getUserRegion(ip);
      userRegions.set(userId, region);
    }
  };

  // API Usage Tracking
  const apiMetrics = {
    youtube: {
      totalCalls: 0,
      todayCalls: 0,
      lastReset: new Date().toDateString(),
      errors: 0,
      quotaExceeded: 0,
      avgResponseTime: 0,
      recentCalls: [] as Array<{ timestamp: Date; status: number; responseTime: number }>
    },
    openai: {
      totalCalls: 0,
      todayCalls: 0,
      lastReset: new Date().toDateString(),
      errors: 0,
      avgResponseTime: 0,
      recentCalls: [] as Array<{ timestamp: Date; status: number; responseTime: number }>
    }
  };

  // Reset daily counters at midnight
  const resetDailyCounters = () => {
    const today = new Date().toDateString();
    if (apiMetrics.youtube.lastReset !== today) {
      apiMetrics.youtube.todayCalls = 0;
      apiMetrics.youtube.lastReset = today;
    }
    if (apiMetrics.openai.lastReset !== today) {
      apiMetrics.openai.todayCalls = 0;
      apiMetrics.openai.lastReset = today;
    }
  };

  // Track OpenAI token usage
  const trackOpenAIUsage = (tokensUsed: number, model: string = 'gpt-3.5-turbo') => {
    // Reset daily counters if new day
    const today = new Date().toDateString();
    if (openaiUsageData.dailyReset !== today) {
      openaiUsageData.dailyTokens = 0;
      openaiUsageData.dailyReset = today;
    }
    
    openaiUsageData.tokensUsed += tokensUsed;
    openaiUsageData.dailyTokens += tokensUsed;
    
    // Estimate cost (rough pricing for GPT-3.5-turbo: $0.002 per 1K tokens)
    const costPer1KTokens = model.includes('gpt-4') ? 0.03 : 0.002;
    openaiUsageData.estimatedCost += (tokensUsed / 1000) * costPer1KTokens;
    
    openaiUsageData.lastUpdated = new Date();
    openaiUsageData.updatedBy = 'auto-tracking';
    
    console.log(`🤖 OpenAI usage: +${tokensUsed} tokens, Total: ${openaiUsageData.tokensUsed}, Cost: $${openaiUsageData.estimatedCost.toFixed(4)}`);
  };
  
  // Track API call and update quota usage
  const trackAPICall = (api: 'youtube' | 'openai', status: number, responseTime: number, tokensUsed?: number) => {
    resetDailyCounters();
    
    const metrics = apiMetrics[api];
    metrics.totalCalls++;
    metrics.todayCalls++;
    
    // Auto-increment YouTube quota usage for successful calls
    if (api === 'youtube' && status === 200) {
      // YouTube search costs 100 quota units per call
      manualQuotaData.quotaUsed = Math.min(manualQuotaData.quotaUsed + 100, 10000);
      manualQuotaData.lastUpdated = new Date();
      manualQuotaData.updatedBy = 'auto-increment';
      console.log(`📊 YouTube quota auto-incremented to ${manualQuotaData.quotaUsed}/10000`);
    }
    
    // Track OpenAI token usage
    if (api === 'openai' && status === 200 && tokensUsed) {
      trackOpenAIUsage(tokensUsed);
    }
    
    if (status >= 400) {
      metrics.errors++;
      if (status === 403 && api === 'youtube') {
        metrics.quotaExceeded++;
        // If quota exceeded, set to maximum
        if (api === 'youtube') {
          manualQuotaData.quotaUsed = 10000;
          manualQuotaData.lastUpdated = new Date();
          manualQuotaData.updatedBy = 'quota-exceeded';
        }
      }
    }
    
    // Update average response time
    metrics.avgResponseTime = (metrics.avgResponseTime + responseTime) / 2;
    
    // Keep recent calls (last 100)
    metrics.recentCalls.push({ timestamp: new Date(), status, responseTime });
    if (metrics.recentCalls.length > 100) {
      metrics.recentCalls.shift();
    }
  };

  // Admin Authentication Middleware
  const isAdmin = (req: any, res: any, next: any) => {
    if (req.session?.isAdmin && req.session?.adminEmail === process.env.ADMIN_EMAIL) {
      next();
    } else {
      res.status(403).json({ message: "Admin access required" });
    }
  };

  // Admin Login
  app.post("/api/admin/login", async (req, res) => {
    try {
      const { email, password } = req.body;
      
      if (email === process.env.ADMIN_EMAIL && password === process.env.ADMIN_PASSWORD) {
        req.session.isAdmin = true;
        req.session.adminEmail = email;
        res.json({ success: true, message: "Admin login successful" });
      } else {
        res.status(401).json({ message: "Invalid admin credentials" });
      }
    } catch (error) {
      console.error("Admin login error:", error);
      res.status(500).json({ message: "Login failed" });
    }
  });

  // Admin Logout
  app.post("/api/admin/logout", (req: any, res) => {
    req.session.isAdmin = false;
    req.session.adminEmail = null;
    res.json({ success: true });
  });

  // Store quota data in memory (could be moved to database for persistence)
  let manualQuotaData = {
    quotaUsed: 0,
    quotaLimit: 10000,
    lastUpdated: new Date(),
    updatedBy: 'system'
  };
  
  // OpenAI usage tracking (tokens and cost)
  let openaiUsageData = {
    tokensUsed: 0,
    estimatedCost: 0, // in USD
    lastUpdated: new Date(),
    updatedBy: 'system',
    dailyTokens: 0,
    dailyReset: new Date().toDateString()
  };

  // Manual quota update endpoint
  app.post("/api/admin/quota/youtube", isAdmin, async (req: any, res) => {
    try {
      const { quotaUsed } = req.body;
      
      if (typeof quotaUsed !== 'number' || quotaUsed < 0 || quotaUsed > 10000) {
        return res.status(400).json({ message: "Invalid quota value. Must be between 0 and 10000." });
      }
      
      manualQuotaData = {
        quotaUsed,
        quotaLimit: 10000,
        lastUpdated: new Date(),
        updatedBy: req.session.adminEmail || 'admin'
      };
      
      res.json({ success: true, quota: manualQuotaData });
    } catch (error) {
      console.error('Error updating quota:', error);
      res.status(500).json({ message: "Failed to update quota" });
    }
  });

  // Clean up inactive/deleted plans permanently
  app.post("/api/admin/cleanup", isAdmin, async (req: any, res) => {
    try {
      // Get inactive plans
      const inactivePlans = await db.select({ id: learningPlans.id }).from(learningPlans).where(eq(learningPlans.isActive, false));
      
      if (inactivePlans.length === 0) {
        return res.json({ success: true, message: "No inactive plans to clean up", deletedCount: 0 });
      }
      
      const planIds = inactivePlans.map(p => p.id);
      
      // Delete related data first (foreign key constraints)
      await db.delete(planVideos).where(sql`${planVideos.planId} IN (${planIds.join(',')})`);
      
      // Delete the inactive plans
      const result = await db.delete(learningPlans).where(eq(learningPlans.isActive, false));
      
      res.json({ 
        success: true, 
        message: `Permanently deleted ${inactivePlans.length} inactive plans and their data`,
        deletedCount: inactivePlans.length
      });
    } catch (error) {
      console.error('Error cleaning up inactive plans:', error);
      res.status(500).json({ message: "Failed to clean up inactive plans" });
    }
  });

  // Get current quota data
  const getYouTubeQuota = () => {
    const quotaRemaining = Math.max(0, manualQuotaData.quotaLimit - manualQuotaData.quotaUsed);
    const percentageUsed = Math.round((manualQuotaData.quotaUsed / manualQuotaData.quotaLimit) * 100);
    
    return {
      quotaUsed: manualQuotaData.quotaUsed,
      quotaLimit: manualQuotaData.quotaLimit,
      quotaRemaining,
      percentageUsed,
      status: manualQuotaData.quotaUsed >= 10000 ? 'Quota Exceeded' : 'Active',
      lastUpdated: manualQuotaData.lastUpdated,
      updatedBy: manualQuotaData.updatedBy
    };
  };

  // Admin Stats
  app.get("/api/admin/stats", isAdmin, async (req: any, res) => {
    try {
      // Get total users
      const totalUsersResult = await db.select({ count: sql<number>`count(*)` }).from(users);
      const totalUsers = totalUsersResult[0]?.count || 0;

      // Get currently active users (real-time)
      const currentlyActiveUsers = activeUsers.size;

      // Get total videos
      const totalVideosResult = await db.select({ count: sql<number>`count(*)` }).from(videos);
      const totalVideos = totalVideosResult[0]?.count || 0;

      // Get total active plans only
      const totalPlansResult = await db.select({ count: sql<number>`count(*)` }).from(learningPlans).where(eq(learningPlans.isActive, true));
      const totalPlans = totalPlansResult[0]?.count || 0;
      
      // Get public active plans count
      const publicPlansResult = await db.select({ count: sql<number>`count(*)` }).from(learningPlans).where(
        and(
          eq(learningPlans.isPublic, true),
          eq(learningPlans.isActive, true)
        )
      );
      const publicPlans = publicPlansResult[0]?.count || 0;

      // Get recent users
      const recentUsers = await db
        .select({
          id: users.id,
          email: users.email,
          firstName: users.firstName,
          lastName: users.lastName,
          createdAt: users.createdAt,
          lastActive: users.updatedAt
        })
        .from(users)
        .orderBy(desc(users.createdAt))
        .limit(10);

      // Get top active plans only
      const topPlans = await db
        .select({
          id: learningPlans.id,
          title: learningPlans.title,
          videoCount: sql<number>`count(distinct ${planVideos.videoId})`,
          userCount: sql<number>`1`
        })
        .from(learningPlans)
        .leftJoin(planVideos, eq(learningPlans.id, planVideos.planId))
        .where(eq(learningPlans.isActive, true))
        .groupBy(learningPlans.id, learningPlans.title)
        .orderBy(desc(sql<number>`count(distinct ${planVideos.videoId})`))
        .limit(10);

      // Get regional analytics
      const regionStats = new Map<string, number>();
      for (const region of userRegions.values()) {
        regionStats.set(region, (regionStats.get(region) || 0) + 1);
      }
      
      const viewersByRegion = Array.from(regionStats.entries())
        .map(([region, count]) => ({ region, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);

      // Get YouTube quota data
      const youtubeQuota = getYouTubeQuota();

      // Reset daily counters before sending stats
      resetDailyCounters();

      const stats = {
        totalUsers,
        activeUsers: currentlyActiveUsers,
        totalVideos,
        totalPlans,
        publicPlans,
        totalProgress: 0,
        recentUsers,
        topPlans,
        apiMetrics: {
          youtube: {
            totalCalls: apiMetrics.youtube.totalCalls,
            todayCalls: apiMetrics.youtube.todayCalls,
            errors: apiMetrics.youtube.errors,
            quotaExceeded: apiMetrics.youtube.quotaExceeded,
            avgResponseTime: Math.round(apiMetrics.youtube.avgResponseTime),
            status: youtubeQuota?.status || (apiMetrics.youtube.quotaExceeded > 0 ? 'Quota Exceeded' : 'Active'),
            recentErrors: apiMetrics.youtube.recentCalls.filter(call => call.status >= 400).length,
            // Real quota data from Google API
            realQuota: youtubeQuota ? {
              used: youtubeQuota.quotaUsed,
              limit: youtubeQuota.quotaLimit,
              remaining: youtubeQuota.quotaRemaining,
              percentageUsed: Math.round((youtubeQuota.quotaUsed / youtubeQuota.quotaLimit) * 100)
            } : null
          },
          openai: {
            totalCalls: apiMetrics.openai.totalCalls,
            todayCalls: apiMetrics.openai.todayCalls,
            errors: apiMetrics.openai.errors,
            avgResponseTime: Math.round(apiMetrics.openai.avgResponseTime),
            status: apiMetrics.openai.errors > 10 ? 'High Error Rate' : 'Active',
            recentErrors: apiMetrics.openai.recentCalls.filter(call => call.status >= 400).length,
            // Real usage data
            realUsage: {
              tokensUsed: openaiUsageData.tokensUsed,
              dailyTokens: openaiUsageData.dailyTokens,
              estimatedCost: Math.round(openaiUsageData.estimatedCost * 100) / 100, // Round to 2 decimals
              lastUpdated: openaiUsageData.lastUpdated,
              updatedBy: openaiUsageData.updatedBy
            }
          }
        },
        systemHealth: {
          dbStatus: "Healthy",
          apiStatus: "Online",
          uptime: process.uptime ? `${Math.floor(process.uptime() / 3600)}h ${Math.floor((process.uptime() % 3600) / 60)}m` : "Unknown"
        },
        viewersByRegion
      };

      res.json(stats);
    } catch (error) {
      console.error("Error fetching admin stats:", error);
      res.status(500).json({ message: "Failed to fetch admin stats" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
