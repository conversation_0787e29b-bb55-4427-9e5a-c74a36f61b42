import passport from "passport";
import { Strategy as LocalStrategy } from "passport-local";
import { Strategy as GoogleStrategy } from "passport-google-oauth20";
import bcrypt from "bcrypt";
import session from "express-session";
import connectPg from "connect-pg-simple";
import type { Express, RequestHandler } from "express";
import { storage } from "./storage";
import { sendPasswordResetEmail } from "./emailService";
import crypto from "crypto";
import { csrfProtection, sanitizeInput, sanitizeForLog } from "./security";

export function setupAuth(app: Express) {
  // Add headers for CloudFront compatibility
  app.use("/api/auth", (req, res, next) => {
    // Prevent caching of auth endpoints
    res.set({
      "Cache-Control": "no-cache, no-store, must-revalidate",
      Pragma: "no-cache",
      Expires: "0",
      Vary: "Cookie, Authorization",
    });
    next();
  });

  // Session configuration - 30 minutes inactivity timeout
  const sessionTtl = 30 * 60 * 1000; // 30 minutes
  const pgStore = connectPg(session);
  const sessionStore = new pgStore({
    conString: process.env.DATABASE_URL,
    createTableIfMissing: false,
    ttl: sessionTtl / 1000, // TTL in seconds for postgres
    tableName: "sessions",
    pruneSessionInterval: 60, // Clean up expired sessions every 60 seconds
  });

  // Additional cleanup job for expired sessions (runs every 5 minutes)
  setInterval(async () => {
    try {
      await sessionStore.pruneSessions?.();
      console.log(sanitizeForLog('🧹 Cleaned up expired sessions'));
    } catch (error) {
      console.error(sanitizeForLog('Session cleanup error:'), error);
    }
  }, 5 * 60 * 1000);

  app.set("trust proxy", true);
  app.use(
    session({
      secret: process.env.SESSION_SECRET!,
      store: sessionStore,
      resave: true, // Resave session on each request to reset TTL
      saveUninitialized: false,
      rolling: true, // Reset expiration on activity
      cookie: {
        httpOnly: true,
        secure: false, // Set to false for now to test if HTTPS is causing issues
        maxAge: sessionTtl,
        sameSite: "lax", // Allow cross-site requests for OAuth
      },
    })
  );

  app.use(passport.initialize());
  app.use(passport.session());

  // Local Strategy (Email/Password)
  passport.use(
    new LocalStrategy(
      {
        usernameField: "email",
        passwordField: "password",
      },
      async (email, password, done) => {
        try {
          const user = await storage.getUserByEmail(email);

          if (!user || !user.passwordHash) {
            return done(null, false, { message: "Invalid email or password" });
          }

          const isValid = await bcrypt.compare(password, user.passwordHash);

          if (!isValid) {
            return done(null, false, { message: "Invalid email or password" });
          }

          return done(null, {
            id: user.id,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            profileImageUrl: user.profileImageUrl,
          });
        } catch (error) {
          console.error(sanitizeForLog("Login error:"), error);
          return done(error);
        }
      }
    )
  );

  // Helper function to determine callback URL based on request
  const getCallbackURL = (req: any) => {
    if (process.env.GOOGLE_CALLBACK_URL) {
      return process.env.GOOGLE_CALLBACK_URL;
    }

    const host = req.get("host");

    if (process.env.NODE_ENV !== "production") {
      return "http://localhost:3000/api/auth/google/callback";
    }

    // Force HTTPS for production domains (CloudFront and main domain)
    const isProductionDomain =
      host?.includes("cloudfront.net") || host?.includes("learniify.com");
    const isSecure =
      req.secure ||
      req.get("x-forwarded-proto") === "https" ||
      isProductionDomain;
    const protocol = isSecure ? "https" : "http";

    // Use the same domain that initiated the request
    return `${protocol}://${host}/api/auth/google/callback`;
  };

  // Google Strategy - Use main domain callback URL
  if (process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET) {
    const callbackURL = process.env.NODE_ENV === 'production' 
      ? "https://www.learniify.com/api/auth/google/callback"
      : "http://localhost:3000/api/auth/google/callback";
      
    passport.use(
      new GoogleStrategy(
        {
          clientID: process.env.GOOGLE_CLIENT_ID,
          clientSecret: process.env.GOOGLE_CLIENT_SECRET,
          callbackURL,
        },
        async (_accessToken, _refreshToken, profile, done) => {
          try {
            const email = profile.emails?.[0]?.value || "";

            // Check if user already exists by email
            let existingUser = await storage.getUserByEmail(email);

            if (existingUser) {
              // User exists, return existing user
              return done(null, {
                id: existingUser.id,
                email: existingUser.email,
                firstName: existingUser.firstName,
                lastName: existingUser.lastName,
                profileImageUrl: existingUser.profileImageUrl,
              });
            }

            // Sanitize inputs
            const sanitizedEmail = sanitizeInput(email);
            const sanitizedFirstName = sanitizeInput(profile.name?.givenName || '');
            const sanitizedLastName = sanitizeInput(profile.name?.familyName || '');

            // Create new user
            await storage.upsertUser({
              id: profile.id,
              email: sanitizedEmail,
              firstName: sanitizedFirstName,
              lastName: sanitizedLastName,
              profileImageUrl: profile.photos?.[0]?.value || null,
            });

            return done(null, {
              id: profile.id,
              email: sanitizedEmail,
              firstName: sanitizedFirstName,
              lastName: sanitizedLastName,
              profileImageUrl: profile.photos?.[0]?.value,
            });
          } catch (error) {
            return done(error);
          }
        }
      )
    );
  }

  passport.serializeUser((user: any, done) => {
    done(null, user.id);
  });

  passport.deserializeUser(async (id: string, done) => {
    try {
      const user = await storage.getUser(id);
      done(null, user);
    } catch (error) {
      done(error);
    }
  });

  // Auth routes
  app.post("/api/auth/signup", csrfProtection, async (req, res) => {
    try {
      const { email, password, firstName, lastName } = req.body;
      
      // Sanitize inputs
      const sanitizedEmail = sanitizeInput(email);
      const sanitizedFirstName = sanitizeInput(firstName || '');
      const sanitizedLastName = sanitizeInput(lastName || '');

      if (!sanitizedEmail || !password) {
        return res
          .status(400)
          .json({ message: "Email and password are required" });
      }

      const existingUser = await storage.getUserByEmail(sanitizedEmail);
      if (existingUser) {
        return res
          .status(400)
          .json({ message: "An account with this email already exists" });
      }

      const passwordHash = await bcrypt.hash(password, 10);
      const userId = Date.now().toString(); // Simple ID generation

      await storage.upsertUser({
        id: userId,
        email: sanitizedEmail,
        firstName: sanitizedFirstName,
        lastName: sanitizedLastName,
        passwordHash,
        profileImageUrl: null,
      });

      const user = { id: userId, email: sanitizedEmail, firstName: sanitizedFirstName, lastName: sanitizedLastName };
      req.login(user, (err: any) => {
        if (err) return res.status(500).json({ message: "Login failed" });
        res.json({ user, message: "Account created successfully" });
      });
    } catch (error) {
      console.error(sanitizeForLog("Signup error:"), error);
      res.status(500).json({ message: "Failed to create account" });
    }
  });

  app.post("/api/auth/login", csrfProtection, (req, res, next) => {
    passport.authenticate("local", (err: any, user: any, info: any) => {
      if (err) {
        return res.status(500).json({ message: "Authentication error" });
      }
      if (!user) {
        return res
          .status(401)
          .json({ message: info?.message || "Invalid credentials" });
      }
      req.logIn(user, (err: any) => {
        if (err) {
          return res.status(500).json({ message: "Login failed" });
        }
        res.json({ user, message: "Login successful" });
      });
    })(req, res, next);
  });

  app.get("/api/auth/google", (req, res, next) => {
    const host = req.get("host");
    const xForwardedHost = req.get("x-forwarded-host");
    const cloudFrontHeader = req.get("cloudfront-viewer-country");

    // Determine the actual user-facing domain
    let actualHost = host;
    if (host?.includes("elb.amazonaws.com")) {
      // Request is coming through ELB, determine actual user domain
      if (cloudFrontHeader) {
        actualHost = "d2m6qztzc8cqux.cloudfront.net";
      } else if (xForwardedHost) {
        actualHost = xForwardedHost;
      } else {
        actualHost = "www.learniify.com"; // fallback
      }
    }

    // Force HTTPS for production domains
    const isProductionDomain =
      actualHost?.includes("cloudfront.net") ||
      actualHost?.includes("learniify.com");
    const isSecure =
      req.secure ||
      req.get("x-forwarded-proto") === "https" ||
      isProductionDomain;
    const protocol = isSecure ? "https" : "http";
    const origin = `${protocol}://${actualHost}`;

    passport.authenticate("google", { scope: ["profile", "email"] })(
      req,
      res,
      next
    );
  });

  app.get("/api/auth/google/callback", (req, res, next) => {
    passport.authenticate("google", (err: any, user: any, info: any) => {
      if (err) {
        console.error(sanitizeForLog("Google OAuth error:"), err);
        return res.redirect("/login?error=oauth_error");
      }

      if (!user) {
        console.error(sanitizeForLog("Google OAuth failed:"), info);
        return res.redirect("/login?error=oauth_failed");
      }

      req.logIn(user, (loginErr: any) => {
        if (loginErr) {
          console.error(sanitizeForLog("Login error after OAuth:"), loginErr);
          return res.redirect("/login?error=login_failed");
        }

        res.redirect("/dashboard");
      });
    })(req, res, next);
  });

  app.post("/api/auth/logout", csrfProtection, (req, res) => {
    // Destroy session completely
    req.session.destroy((err: any) => {
      if (err) {
        console.error(sanitizeForLog('Session destruction error:'), err);
      }
    });
    
    req.logout((err: any) => {
      if (err) return res.status(500).json({ message: "Logout failed" });
      
      // Clear session cookie
      res.clearCookie('connect.sid');
      res.json({ message: "Logged out successfully" });
    });
  });

  app.get("/api/auth/user", (req, res) => {
    if (req.isAuthenticated()) {
      res.json(req.user);
    } else {
      res.status(401).json({ message: "Unauthorized" });
    }
  });

  // Password Reset Routes
  app.post("/api/auth/forgot-password", csrfProtection, async (req, res) => {
    try {
      const { email } = req.body;
      
      // Sanitize email input
      const sanitizedEmail = sanitizeInput(email || '');

      if (!sanitizedEmail) {
        return res.status(400).json({ message: "Email is required" });
      }

      // Check if user exists
      const user = await storage.getUserByEmail(sanitizedEmail);
      if (!user) {
        // Don't reveal if email exists or not for security
        return res.json({
          message:
            "If an account with that email exists, we've sent a password reset link.",
        });
      }

      // Only allow password reset for users with password (not OAuth-only users)
      if (!user.passwordHash) {
        return res.status(400).json({
          message:
            "This account uses Google sign-in. Please sign in with Google instead.",
        });
      }

      // Generate reset token
      const resetToken = crypto.randomBytes(32).toString("hex");
      const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 hour from now

      // Store reset token in database
      await storage.createPasswordResetToken({
        userId: user.id,
        token: resetToken,
        expiresAt,
        isUsed: false,
      });

      // Send reset email
      await sendPasswordResetEmail(
        sanitizedEmail,
        resetToken,
        user.firstName || undefined
      );

      res.json({
        message:
          "If an account with that email exists, we've sent a password reset link.",
      });
    } catch (error) {
      console.error(sanitizeForLog("Forgot password error:"), error);
      res
        .status(500)
        .json({ message: "Failed to process password reset request" });
    }
  });

  app.post("/api/auth/reset-password", csrfProtection, async (req, res) => {
    try {
      const { token, newPassword } = req.body;
      
      // Sanitize token input
      const sanitizedToken = sanitizeInput(token || '');

      if (!sanitizedToken || !newPassword) {
        return res
          .status(400)
          .json({ message: "Token and new password are required" });
      }

      if (newPassword.length < 6) {
        return res
          .status(400)
          .json({ message: "Password must be at least 6 characters long" });
      }

      // Find and validate reset token
      const resetToken = await storage.getPasswordResetToken(sanitizedToken);
      if (
        !resetToken ||
        resetToken.isUsed ||
        new Date() > resetToken.expiresAt
      ) {
        return res
          .status(400)
          .json({ message: "Invalid or expired reset token" });
      }

      // Get user
      const user = await storage.getUser(resetToken.userId);
      if (!user) {
        return res.status(400).json({ message: "User not found" });
      }

      // Hash new password
      const passwordHash = await bcrypt.hash(newPassword, 10);

      // Update user password
      await storage.updateUserPassword(user.id, passwordHash);

      // Mark token as used
      await storage.markPasswordResetTokenAsUsed(sanitizedToken);

      res.json({ message: "Password reset successfully" });
    } catch (error) {
      console.error(sanitizeForLog("Reset password error:"), error);
      res.status(500).json({ message: "Failed to reset password" });
    }
  });

  app.get("/api/auth/verify-reset-token/:token", async (req, res) => {
    try {
      const { token } = req.params;
      
      // Sanitize token parameter
      const sanitizedToken = sanitizeInput(token || '');

      const resetToken = await storage.getPasswordResetToken(sanitizedToken);
      if (
        !resetToken ||
        resetToken.isUsed ||
        new Date() > resetToken.expiresAt
      ) {
        return res
          .status(400)
          .json({ valid: false, message: "Invalid or expired reset token" });
      }

      res.json({ valid: true });
    } catch (error) {
      console.error(sanitizeForLog("Verify reset token error:"), error);
      res.status(500).json({ valid: false, message: "Failed to verify token" });
    }
  });
}

export const isAuthenticated: RequestHandler = (req, res, next) => {
  if (req.isAuthenticated()) {
    return next();
  }
  res.status(401).json({ message: "Unauthorized" });
};
